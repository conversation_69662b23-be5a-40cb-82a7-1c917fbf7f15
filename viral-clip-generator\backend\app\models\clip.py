"""
Clip model for storing generated video clips
"""

from sqlalchemy import Column, Integer, String, DateTime, Float, Text, Boolean, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from ..core.database import Base

class Clip(Base):
    __tablename__ = "clips"
    
    id = Column(Integer, primary_key=True, index=True)
    video_id = Column(Integer, ForeignKey("videos.id"), nullable=False)
    
    # File information
    filename = Column(String, nullable=False)
    file_path = Column(String, nullable=False)
    file_size = Column(Integer, nullable=False)  # in bytes
    
    # Clip timing
    start_time = Column(Float, nullable=False)  # in seconds
    end_time = Column(Float, nullable=False)  # in seconds
    duration = Column(Float, nullable=False)  # in seconds
    
    # Video properties
    width = Column(Integer, default=1080)
    height = Column(Integer, default=1920)  # 9:16 aspect ratio
    fps = Column(Float, default=30)
    
    # Content analysis
    title = Column(String, nullable=True)
    description = Column(Text, nullable=True)
    transcript_segment = Column(Text, nullable=True)
    viral_score = Column(Float, nullable=True)  # 0-100 score
    sentiment = Column(String, nullable=True)  # positive, negative, neutral
    emotion = Column(String, nullable=True)  # happy, sad, angry, surprised, etc.
    
    # Subtitle information
    has_subtitles = Column(Boolean, default=False)
    subtitle_style = Column(String, default="default")
    subtitle_file_path = Column(String, nullable=True)
    
    # Audio information
    has_background_music = Column(Boolean, default=False)
    background_music_path = Column(String, nullable=True)
    music_genre = Column(String, nullable=True)
    
    # Processing status
    status = Column(String, default="pending")  # pending, processing, completed, failed
    
    # Thumbnail
    thumbnail_path = Column(String, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    video = relationship("Video", back_populates="clips")
    
    def __repr__(self):
        return f"<Clip(id={self.id}, video_id={self.video_id}, duration={self.duration}s, viral_score={self.viral_score})>"
