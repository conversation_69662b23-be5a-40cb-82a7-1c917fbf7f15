#!/usr/bin/env python3

import subprocess
import os
from pathlib import Path

def test_ffmpeg_subtitle_command():
    """Test the exact FFmpeg command that's failing"""
    
    print("🔍 TESTING FFMPEG SUBTITLE COMMAND")
    print("=" * 50)
    
    # Use existing files
    video_path = "uploads/videos/70454b9d-5b85-4202-8bf8-b55f52a1ccbe.mp4"
    subtitle_path = "uploads/clips/temp_animated_subtitles_1a3718d8.ass"
    output_path = "uploads/clips/FFMPEG_TEST.mp4"
    
    if not os.path.exists(video_path):
        print(f"❌ Video not found: {video_path}")
        return
        
    if not os.path.exists(subtitle_path):
        print(f"❌ Subtitle file not found: {subtitle_path}")
        return
    
    print(f"✅ Video: {video_path}")
    print(f"✅ Subtitle: {subtitle_path}")
    print(f"📁 Output: {output_path}")
    
    # Test different path escaping methods
    test_cases = [
        {
            "name": "Current Method (Double Backslash)",
            "subtitle_filter": f"ass='{subtitle_path.replace(chr(92), chr(92)+chr(92))}'"
        },
        {
            "name": "Forward Slashes",
            "subtitle_filter": f"ass='{subtitle_path.replace(chr(92), '/')}'"
        },
        {
            "name": "Raw Path",
            "subtitle_filter": f"ass='{subtitle_path}'"
        },
        {
            "name": "Absolute Path",
            "subtitle_filter": f"ass='{os.path.abspath(subtitle_path)}'"
        },
        {
            "name": "Absolute Path with Forward Slashes",
            "subtitle_filter": f"ass='{os.path.abspath(subtitle_path).replace(chr(92), '/')}'"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 TEST {i}: {test_case['name']}")
        print("-" * 40)
        
        test_output = f"uploads/clips/FFMPEG_TEST_{i}.mp4"
        
        cmd = [
            'ffmpeg',
            '-i', video_path,
            '-ss', '0',
            '-t', '3',
            '-c:v', 'libx264',
            '-c:a', 'aac',
            '-preset', 'fast',
            '-crf', '23',
            '-vf', f"crop=ih*9/16:ih,{test_case['subtitle_filter']}",
            '-y', test_output
        ]
        
        print(f"📝 Filter: {test_case['subtitle_filter']}")
        print(f"🎬 Command: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            print(f"📊 Return code: {result.returncode}")
            
            if result.returncode == 0:
                if os.path.exists(test_output):
                    file_size = os.path.getsize(test_output)
                    print(f"✅ SUCCESS! Output: {file_size:,} bytes")
                    
                    # Quick check if subtitles are embedded
                    # Larger file size usually indicates subtitles are embedded
                    if file_size > 100000:  # > 100KB
                        print(f"🎯 File size suggests subtitles are embedded!")
                    else:
                        print(f"⚠️  Small file size - subtitles might not be embedded")
                else:
                    print(f"❌ Output file not created")
            else:
                print(f"❌ FAILED!")
                if result.stderr:
                    print(f"📤 Error: {result.stderr[:200]}...")
                    
        except subprocess.TimeoutExpired:
            print(f"⏰ TIMEOUT!")
        except Exception as e:
            print(f"💥 EXCEPTION: {e}")
    
    print(f"\n🎯 SUMMARY")
    print("=" * 50)
    print("Check which test case produces a working video with visible subtitles!")
    print("The successful method should be used in the video processor.")

if __name__ == "__main__":
    test_ffmpeg_subtitle_command()
