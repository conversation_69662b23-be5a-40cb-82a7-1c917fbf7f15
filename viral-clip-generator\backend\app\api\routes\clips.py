"""
Clips API routes for managing generated video clips
"""

from fastapi import APIRouter, HTTPException, Depends, Response
from fastapi.responses import FileResponse
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional
import os
import zipfile
import tempfile
import logging

from ...core.database import get_db
from ...models.video import Video
from ...models.clip import Clip

logger = logging.getLogger(__name__)
router = APIRouter()

@router.get("/video/{video_id}")
async def get_clips_for_video(video_id: int, db: AsyncSession = Depends(get_db)):
    """Get all clips generated for a specific video"""
    
    video = await db.get(Video, video_id)
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")
    
    from sqlalchemy import select
    query = select(Clip).where(Clip.video_id == video_id).order_by(Clip.viral_score.desc())
    result = await db.execute(query)
    clips = result.scalars().all()
    
    return {
        "video_id": video_id,
        "video_title": video.title or video.original_filename,
        "clips": [
            {
                "id": clip.id,
                "title": clip.title,
                "description": clip.description,
                "duration": clip.duration,
                "start_time": clip.start_time,
                "end_time": clip.end_time,
                "viral_score": clip.viral_score,
                "sentiment": clip.sentiment,
                "emotion": clip.emotion,
                "has_subtitles": clip.has_subtitles,
                "has_background_music": clip.has_background_music,
                "status": clip.status,
                "thumbnail_url": f"/api/clips/{clip.id}/thumbnail" if clip.thumbnail_path else None,
                "download_url": f"/api/clips/{clip.id}/download",
                "created_at": clip.created_at
            }
            for clip in clips
        ],
        "total_clips": len(clips)
    }

@router.get("/{clip_id}")
async def get_clip_details(clip_id: int, db: AsyncSession = Depends(get_db)):
    """Get detailed information about a specific clip"""
    
    clip = await db.get(Clip, clip_id)
    if not clip:
        raise HTTPException(status_code=404, detail="Clip not found")
    
    return {
        "id": clip.id,
        "video_id": clip.video_id,
        "title": clip.title,
        "description": clip.description,
        "transcript_segment": clip.transcript_segment,
        "duration": clip.duration,
        "start_time": clip.start_time,
        "end_time": clip.end_time,
        "width": clip.width,
        "height": clip.height,
        "fps": clip.fps,
        "file_size": clip.file_size,
        "viral_score": clip.viral_score,
        "sentiment": clip.sentiment,
        "emotion": clip.emotion,
        "has_subtitles": clip.has_subtitles,
        "subtitle_style": clip.subtitle_style,
        "has_background_music": clip.has_background_music,
        "music_genre": clip.music_genre,
        "status": clip.status,
        "thumbnail_url": f"/api/clips/{clip.id}/thumbnail" if clip.thumbnail_path else None,
        "download_url": f"/api/clips/{clip.id}/download",
        "created_at": clip.created_at,
        "updated_at": clip.updated_at
    }

@router.get("/{clip_id}/download")
async def download_clip(clip_id: int, db: AsyncSession = Depends(get_db)):
    """Download a specific clip file"""
    
    clip = await db.get(Clip, clip_id)
    if not clip:
        raise HTTPException(status_code=404, detail="Clip not found")
    
    if not os.path.exists(clip.file_path):
        raise HTTPException(status_code=404, detail="Clip file not found")
    
    filename = f"{clip.title or f'clip_{clip.id}'}.mp4"
    
    return FileResponse(
        path=clip.file_path,
        filename=filename,
        media_type='video/mp4'
    )

@router.get("/{clip_id}/thumbnail")
async def get_clip_thumbnail(clip_id: int, db: AsyncSession = Depends(get_db)):
    """Get thumbnail image for a clip"""
    
    clip = await db.get(Clip, clip_id)
    if not clip:
        raise HTTPException(status_code=404, detail="Clip not found")
    
    if not clip.thumbnail_path or not os.path.exists(clip.thumbnail_path):
        raise HTTPException(status_code=404, detail="Thumbnail not found")
    
    return FileResponse(
        path=clip.thumbnail_path,
        media_type='image/png'
    )

@router.delete("/{clip_id}")
async def delete_clip(clip_id: int, db: AsyncSession = Depends(get_db)):
    """Delete a specific clip"""
    
    clip = await db.get(Clip, clip_id)
    if not clip:
        raise HTTPException(status_code=404, detail="Clip not found")
    
    try:
        # Delete files
        if os.path.exists(clip.file_path):
            os.remove(clip.file_path)
        
        if clip.thumbnail_path and os.path.exists(clip.thumbnail_path):
            os.remove(clip.thumbnail_path)
        
        if clip.subtitle_file_path and os.path.exists(clip.subtitle_file_path):
            os.remove(clip.subtitle_file_path)
        
        # Delete from database
        await db.delete(clip)
        await db.commit()
        
        logger.info(f"Clip deleted successfully: {clip_id}")
        
        return {"message": "Clip deleted successfully"}
        
    except Exception as e:
        logger.error(f"Error deleting clip {clip_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error deleting clip: {str(e)}")

@router.post("/video/{video_id}/download-all")
async def download_all_clips(video_id: int, db: AsyncSession = Depends(get_db)):
    """Download all clips for a video as a ZIP file"""
    
    video = await db.get(Video, video_id)
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")
    
    from sqlalchemy import select
    query = select(Clip).where(Clip.video_id == video_id, Clip.status == "completed")
    result = await db.execute(query)
    clips = result.scalars().all()
    
    if not clips:
        raise HTTPException(status_code=404, detail="No completed clips found")
    
    try:
        # Create temporary ZIP file
        with tempfile.NamedTemporaryFile(delete=False, suffix='.zip') as tmp_file:
            with zipfile.ZipFile(tmp_file.name, 'w', zipfile.ZIP_DEFLATED) as zip_file:
                for clip in clips:
                    if os.path.exists(clip.file_path):
                        # Add clip to ZIP
                        clip_name = f"{clip.title or f'clip_{clip.id}'}.mp4"
                        zip_file.write(clip.file_path, clip_name)
                        
                        # Add thumbnail if exists
                        if clip.thumbnail_path and os.path.exists(clip.thumbnail_path):
                            thumb_name = f"{clip.title or f'clip_{clip.id}'}_thumbnail.png"
                            zip_file.write(clip.thumbnail_path, thumb_name)
            
            # Return ZIP file
            video_title = video.title or video.original_filename
            zip_filename = f"{video_title}_clips.zip"
            
            return FileResponse(
                path=tmp_file.name,
                filename=zip_filename,
                media_type='application/zip'
            )
    
    except Exception as e:
        logger.error(f"Error creating ZIP file for video {video_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error creating download: {str(e)}")

@router.put("/{clip_id}")
async def update_clip(
    clip_id: int,
    title: Optional[str] = None,
    description: Optional[str] = None,
    db: AsyncSession = Depends(get_db)
):
    """Update clip metadata"""
    
    clip = await db.get(Clip, clip_id)
    if not clip:
        raise HTTPException(status_code=404, detail="Clip not found")
    
    try:
        if title is not None:
            clip.title = title
        if description is not None:
            clip.description = description
        
        await db.commit()
        await db.refresh(clip)
        
        return {
            "message": "Clip updated successfully",
            "clip": {
                "id": clip.id,
                "title": clip.title,
                "description": clip.description
            }
        }
        
    except Exception as e:
        logger.error(f"Error updating clip {clip_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error updating clip: {str(e)}")

@router.get("/")
async def list_all_clips(
    skip: int = 0,
    limit: int = 50,
    sort_by: str = "viral_score",
    db: AsyncSession = Depends(get_db)
):
    """List all clips with pagination and sorting"""
    
    from sqlalchemy import select, desc
    
    # Build query with sorting
    query = select(Clip).where(Clip.status == "completed")
    
    if sort_by == "viral_score":
        query = query.order_by(desc(Clip.viral_score))
    elif sort_by == "created_at":
        query = query.order_by(desc(Clip.created_at))
    elif sort_by == "duration":
        query = query.order_by(desc(Clip.duration))
    
    query = query.offset(skip).limit(limit)
    
    result = await db.execute(query)
    clips = result.scalars().all()
    
    return {
        "clips": [
            {
                "id": clip.id,
                "video_id": clip.video_id,
                "title": clip.title,
                "duration": clip.duration,
                "viral_score": clip.viral_score,
                "sentiment": clip.sentiment,
                "emotion": clip.emotion,
                "thumbnail_url": f"/api/clips/{clip.id}/thumbnail" if clip.thumbnail_path else None,
                "download_url": f"/api/clips/{clip.id}/download",
                "created_at": clip.created_at
            }
            for clip in clips
        ],
        "total": len(clips),
        "skip": skip,
        "limit": limit
    }
