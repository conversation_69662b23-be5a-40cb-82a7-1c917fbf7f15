import asyncio
import os
import sys
import json
from pathlib import Path

# Add the app directory to the Python path
sys.path.append(str(Path(__file__).parent / "app"))

from app.services.ai_processor import AIProcessor
from app.services.video_processor import VideoProcessor

async def verify_subtitle_fix():
    """Verify that the subtitle fix works by testing the exact same process as viral clip generation"""
    
    print("✅ VERIFYING SUBTITLE FIX FOR VIRAL CLIP GENERATION")
    print("=" * 70)
    
    # Check if test video exists
    test_video_path = "uploads/videos/test-video.mp4"
    if not os.path.exists(test_video_path):
        print("❌ Test video not found!")
        return
    
    print(f"✅ Test video found: {test_video_path}")
    
    # Initialize components
    class MockWebSocketManager:
        async def send_processing_update(self, *args, **kwargs):
            pass
        async def send_processing_complete(self, *args, **kwargs):
            pass
        async def send_clip_generated(self, *args, **kwargs):
            pass
    
    class MockVideo:
        def __init__(self):
            self.id = 1000
            self.file_path = test_video_path
            self.transcript = None
            self.transcript_with_timestamps = None
    
    class MockDB:
        async def commit(self):
            pass
    
    ai_processor = AIProcessor(MockWebSocketManager())
    video_processor = VideoProcessor()
    video = MockVideo()
    db = MockDB()
    
    print("\n🎤 STEP 1: TRANSCRIBE AUDIO WITH WORD TIMESTAMPS")
    print("-" * 60)
    
    # Extract audio and transcribe
    audio_path = await ai_processor._extract_audio(video)
    if not audio_path:
        print("❌ Audio extraction failed")
        return
    
    transcript_data = await ai_processor._transcribe_audio(audio_path, video, db)
    if not transcript_data:
        print("❌ Transcription failed")
        return
    
    all_segments = transcript_data.get('segments', [])
    print(f"✅ Transcription complete: {len(all_segments)} segments")
    
    # Verify segments have words
    segments_with_words = [s for s in all_segments if 'words' in s and s['words']]
    print(f"✅ Segments with word timing: {len(segments_with_words)}/{len(all_segments)}")
    
    if len(segments_with_words) == 0:
        print("❌ NO SEGMENTS HAVE WORD TIMING - FIX FAILED!")
        return
    
    print("\n🔥 STEP 2: SIMULATE VIRAL CLIP GENERATION")
    print("-" * 60)
    
    # Simulate the exact process from _generate_clips method
    # Create a mock viral segment (like what would come from viral moment detection)
    mock_viral_segment = {
        "start_time": 10.0,
        "end_time": 25.0,
        "duration": 15.0,
        "text": "Test viral moment",
        "viral_score": 0.85,
        "sentiment": "POSITIVE",
        "emotion": "excited"
    }
    
    clip_start = mock_viral_segment["start_time"]
    clip_end = mock_viral_segment["end_time"]
    
    # Extract subtitle segments for this clip (EXACT same logic as in ai_processor.py)
    clip_subtitle_segments = []
    for transcript_seg in all_segments:
        seg_start = transcript_seg.get('start', 0)
        seg_end = transcript_seg.get('end', 0)
        
        # Check if segment overlaps with clip timeframe
        if not (seg_end <= clip_start or seg_start >= clip_end):
            clip_subtitle_segments.append(transcript_seg)
    
    print(f"📝 Clip timeframe: {clip_start:.1f}s - {clip_end:.1f}s")
    print(f"📝 Subtitle segments for clip: {len(clip_subtitle_segments)}")
    
    # Show sample segments
    for i, seg in enumerate(clip_subtitle_segments[:3]):
        words_count = len(seg.get('words', []))
        print(f"   {i+1}: {seg['start']:.1f}s-{seg['end']:.1f}s '{seg['text'][:50]}...' ({words_count} words)")
    
    print("\n🎬 STEP 3: GENERATE CLIP WITH SUBTITLES")
    print("-" * 60)
    
    # Generate clip using the EXACT same method as in ai_processor.py
    clip_path = "uploads/clips/VERIFIED_SUBTITLE_FIX.mp4"
    
    success = await video_processor.extract_clip(
        video.file_path,
        mock_viral_segment["start_time"],
        mock_viral_segment["end_time"],
        clip_path,
        crop_to_vertical=True,
        subtitle_segments=clip_subtitle_segments  # This should now have words!
    )
    
    if success and os.path.exists(clip_path):
        file_size = os.path.getsize(clip_path)
        print(f"✅ Clip generated successfully: {file_size:,} bytes")
        print(f"📁 Location: {clip_path}")
        
        # Create comparison without subtitles
        print("\n📊 STEP 4: CREATE COMPARISON (NO SUBTITLES)")
        print("-" * 60)
        
        import subprocess
        
        no_subs_path = "uploads/clips/NO_SUBS_COMPARISON.mp4"
        cmd = [
            'ffmpeg', '-i', video.file_path,
            '-ss', str(clip_start), '-t', str(clip_end - clip_start),
            '-c:v', 'libx264', '-c:a', 'aac', '-preset', 'fast', '-crf', '23',
            '-vf', 'crop=ih*9/16:ih', '-y', no_subs_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0 and os.path.exists(no_subs_path):
            no_subs_size = os.path.getsize(no_subs_path)
            size_diff = file_size - no_subs_size
            size_diff_percent = (size_diff / no_subs_size) * 100
            
            print(f"✅ Comparison created: {no_subs_size:,} bytes")
            print(f"📊 Size analysis:")
            print(f"   With subtitles:    {file_size:,} bytes")
            print(f"   Without subtitles: {no_subs_size:,} bytes")
            print(f"   Difference:        {size_diff:+,} bytes ({size_diff_percent:+.1f}%)")
            
            if size_diff_percent > 5:
                print("✅ SIGNIFICANT SIZE DIFFERENCE - SUBTITLES ARE BEING EMBEDDED!")
                
                print("\n🎯 STEP 5: VERIFY SUBTITLE FILE CREATION")
                print("-" * 60)
                
                # Test subtitle file creation directly
                output_dir = Path("uploads/clips")
                subtitle_file = await video_processor._create_subtitle_file(
                    clip_subtitle_segments, clip_start, clip_end, output_dir
                )
                
                if subtitle_file and os.path.exists(subtitle_file):
                    with open(subtitle_file, 'r', encoding='utf-8') as f:
                        subtitle_content = f.read()
                    
                    dialogue_lines = [line for line in subtitle_content.splitlines() if line.startswith('Dialogue:')]
                    print(f"✅ Subtitle file created: {len(subtitle_content)} chars")
                    print(f"✅ Dialogue events: {len(dialogue_lines)}")
                    
                    if dialogue_lines:
                        print("✅ SUBTITLE EVENTS FOUND - SUBTITLES WILL BE VISIBLE!")
                        
                        # Show sample dialogue
                        print(f"\n📝 Sample dialogue line:")
                        print(f"   {dialogue_lines[0][:100]}...")
                        
                    else:
                        print("❌ NO DIALOGUE EVENTS - SUBTITLES WON'T BE VISIBLE!")
                    
                    # Clean up
                    os.remove(subtitle_file)
                else:
                    print("❌ Failed to create subtitle file")
                    
            else:
                print("⚠️  Small size difference - subtitles might not be working properly")
        else:
            print("❌ Failed to create comparison clip")
    else:
        print("❌ Failed to generate clip with subtitles")
    
    # Clean up
    if os.path.exists(audio_path):
        os.remove(audio_path)
    
    print("\n🎉 SUBTITLE FIX VERIFICATION COMPLETE!")
    print("=" * 70)
    print("📺 If successful, all future viral clips will have visible subtitles!")

if __name__ == "__main__":
    asyncio.run(verify_subtitle_fix())
