import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Toaster } from "@/components/ui/sonner";
import { ThemeProvider } from "@/components/theme-provider";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Viral Clip Generator - AI-Powered Short Video Creation",
  description: "Transform long-form videos into viral short clips with AI-powered transcription, moment detection, and automatic subtitle generation.",
  keywords: ["AI", "video editing", "viral clips", "short videos", "TikTok", "Reels", "transcription"],
  authors: [{ name: "Viral Clip Generator" }],
  openGraph: {
    title: "Viral Clip Generator",
    description: "AI-Powered Short Video Creation",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${inter.className} antialiased`}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <div className="min-h-screen bg-background">
            {children}
          </div>
          <Toaster />
        </ThemeProvider>
      </body>
    </html>
  );
}
