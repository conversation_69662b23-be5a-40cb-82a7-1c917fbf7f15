#!/usr/bin/env python3

import asyncio
import os
import sys
from pathlib import Path

# Add the app directory to Python path
sys.path.append(str(Path(__file__).parent / 'app'))

from app.services.video_processor import VideoProcessor

async def debug_subtitle_files():
    """Debug subtitle file creation for multiple clips"""
    
    print("🔍 DEBUGGING SUBTITLE FILE CREATION FOR MULTIPLE CLIPS")
    print("=" * 70)
    
    # Find any available video
    videos_dir = Path("uploads/videos")
    video_files = list(videos_dir.glob("*.mp4"))

    if not video_files:
        print(f"❌ No video files found in {videos_dir}")
        return

    test_video_path = str(video_files[0])
    print(f"✅ Using video: {test_video_path}")

    
    # Mock subtitle segments for different clips
    clip_1_segments = [
        {
            'start': 0.0,
            'end': 3.0,
            'text': 'This is clip one content',
            'words': [
                {'word': ' This', 'start': 0.0, 'end': 0.5, 'confidence': 0.9},
                {'word': ' is', 'start': 0.5, 'end': 0.8, 'confidence': 0.95},
                {'word': ' clip', 'start': 0.8, 'end': 1.2, 'confidence': 0.9},
                {'word': ' one', 'start': 1.2, 'end': 1.6, 'confidence': 0.95},
                {'word': ' content', 'start': 1.6, 'end': 3.0, 'confidence': 0.9}
            ]
        }
    ]
    
    clip_2_segments = [
        {
            'start': 5.0,
            'end': 8.0,
            'text': 'This is clip two different content',
            'words': [
                {'word': ' This', 'start': 5.0, 'end': 5.4, 'confidence': 0.9},
                {'word': ' is', 'start': 5.4, 'end': 5.7, 'confidence': 0.95},
                {'word': ' clip', 'start': 5.7, 'end': 6.1, 'confidence': 0.9},
                {'word': ' two', 'start': 6.1, 'end': 6.5, 'confidence': 0.95},
                {'word': ' different', 'start': 6.5, 'end': 7.2, 'confidence': 0.9},
                {'word': ' content', 'start': 7.2, 'end': 8.0, 'confidence': 0.9}
            ]
        }
    ]
    
    clip_3_segments = [
        {
            'start': 10.0,
            'end': 13.0,
            'text': 'This is clip three unique content',
            'words': [
                {'word': ' This', 'start': 10.0, 'end': 10.4, 'confidence': 0.9},
                {'word': ' is', 'start': 10.4, 'end': 10.7, 'confidence': 0.95},
                {'word': ' clip', 'start': 10.7, 'end': 11.1, 'confidence': 0.9},
                {'word': ' three', 'start': 11.1, 'end': 11.6, 'confidence': 0.95},
                {'word': ' unique', 'start': 11.6, 'end': 12.2, 'confidence': 0.9},
                {'word': ' content', 'start': 12.2, 'end': 13.0, 'confidence': 0.9}
            ]
        }
    ]
    
    processor = VideoProcessor()
    
    # Test creating 3 different clips with different subtitle content
    clips_data = [
        {"name": "CLIP_1", "start": 0.0, "end": 3.0, "segments": clip_1_segments},
        {"name": "CLIP_2", "start": 5.0, "end": 8.0, "segments": clip_2_segments},
        {"name": "CLIP_3", "start": 10.0, "end": 13.0, "segments": clip_3_segments}
    ]
    
    print(f"\n🎬 CREATING {len(clips_data)} CLIPS WITH DIFFERENT SUBTITLES")
    print("-" * 70)
    
    for i, clip_data in enumerate(clips_data, 1):
        print(f"\n📹 CLIP {i}: {clip_data['name']}")
        print(f"   Time: {clip_data['start']}s - {clip_data['end']}s")
        print(f"   Segments: {len(clip_data['segments'])}")
        print(f"   Text: '{clip_data['segments'][0]['text']}'")
        
        clip_path = f"uploads/clips/DEBUG_{clip_data['name']}.mp4"
        
        success = await processor.extract_clip(
            test_video_path,
            clip_data['start'],
            clip_data['end'],
            clip_path,
            crop_to_vertical=True,
            subtitle_segments=clip_data['segments']
        )
        
        if success and os.path.exists(clip_path):
            file_size = os.path.getsize(clip_path)
            print(f"   ✅ Clip created: {file_size:,} bytes")
        else:
            print(f"   ❌ Clip creation failed")
    
    print(f"\n📁 CHECKING SUBTITLE FILES IN uploads/clips/")
    print("-" * 70)
    
    clips_dir = Path("uploads/clips")
    subtitle_files = list(clips_dir.glob("temp_animated_subtitles_*.ass"))
    
    print(f"Found {len(subtitle_files)} subtitle files:")
    for subtitle_file in subtitle_files:
        file_size = subtitle_file.stat().st_size
        print(f"   📝 {subtitle_file.name} ({file_size} bytes)")
        
        # Show first few lines of each subtitle file
        try:
            with open(subtitle_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                dialogue_lines = [line for line in lines if line.startswith('Dialogue:')]
                print(f"      Dialogues: {len(dialogue_lines)}")
                if dialogue_lines:
                    first_dialogue = dialogue_lines[0][:100] + "..." if len(dialogue_lines[0]) > 100 else dialogue_lines[0]
                    print(f"      Sample: {first_dialogue.strip()}")
        except Exception as e:
            print(f"      ❌ Error reading file: {e}")
    
    print(f"\n🎯 SUMMARY")
    print("=" * 70)
    print(f"✅ Each clip should create its own unique subtitle file")
    print(f"✅ Each subtitle file should contain different content")
    print(f"✅ Subtitle files are now preserved for debugging")
    print(f"🔍 Check if FFmpeg is actually using these files correctly")

if __name__ == "__main__":
    asyncio.run(debug_subtitle_files())
