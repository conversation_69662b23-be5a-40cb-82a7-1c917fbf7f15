import asyncio
import os
import sys
import json
import sqlite3
import subprocess
from pathlib import Path

# Add the app directory to the Python path
sys.path.append(str(Path(__file__).parent / "app"))

from app.services.video_processor import VideoProcessor

async def test_actual_clip_subtitles():
    """Test if subtitles are actually being generated correctly in real clips"""
    
    print("🔍 TESTING ACTUAL CLIP SUBTITLE GENERATION")
    print("=" * 60)
    
    # Get the most recent clip
    conn = sqlite3.connect('viral_clips.db')
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT c.*, v.transcript_with_timestamps 
        FROM clips c 
        JOIN videos v ON c.video_id = v.id 
        WHERE c.has_subtitles = 1 
        ORDER BY c.id DESC 
        LIMIT 1
    ''')
    
    result = cursor.fetchone()
    if not result:
        print("❌ No clips with subtitles found!")
        return
    
    # Extract clip data
    clip_id = result[0]
    video_id = result[1]
    clip_filename = result[2]
    clip_path = result[3]
    start_time = result[5]
    end_time = result[6]
    transcript_with_timestamps = result[-1]
    
    print(f"✅ Testing clip: ID {clip_id} - {clip_filename}")
    print(f"📁 Path: {clip_path}")
    print(f"⏱️  Time: {start_time:.1f}s - {end_time:.1f}s")
    
    # Check if clip file exists
    if not os.path.exists(clip_path):
        print(f"❌ Clip file not found: {clip_path}")
        return
    
    file_size = os.path.getsize(clip_path)
    print(f"📊 File size: {file_size:,} bytes")
    
    # Parse transcript data
    try:
        transcript_data = json.loads(transcript_with_timestamps)
        all_segments = transcript_data.get('segments', [])
        print(f"📝 Total transcript segments: {len(all_segments)}")
        
        # Extract subtitle segments for this clip (same logic as _generate_clips)
        clip_subtitle_segments = []
        for transcript_seg in all_segments:
            seg_start = transcript_seg.get('start', 0)
            seg_end = transcript_seg.get('end', 0)
            
            # Check if segment overlaps with clip timeframe
            if not (seg_end <= start_time or seg_start >= end_time):
                clip_subtitle_segments.append(transcript_seg)
        
        print(f"📝 Subtitle segments for this clip: {len(clip_subtitle_segments)}")
        
        # Check if these segments have words
        segments_with_words = [s for s in clip_subtitle_segments if 'words' in s and s['words']]
        print(f"📝 Segments with words: {len(segments_with_words)}/{len(clip_subtitle_segments)}")
        
        if len(segments_with_words) == 0:
            print("❌ CLIP SEGMENTS DON'T HAVE WORDS!")
            return
        
        # Show sample segments
        print(f"\n📋 SAMPLE SUBTITLE SEGMENTS:")
        for i, seg in enumerate(clip_subtitle_segments[:3]):
            words_count = len(seg.get('words', []))
            print(f"   {i+1}: {seg['start']:.1f}s-{seg['end']:.1f}s '{seg['text'][:40]}...' ({words_count} words)")
        
        print(f"\n🔧 STEP 1: RECREATE SUBTITLE FILE")
        print("-" * 40)
        
        # Test subtitle file creation with the EXACT same data
        processor = VideoProcessor()
        output_dir = Path("uploads/clips")
        
        subtitle_file = await processor._create_subtitle_file(
            clip_subtitle_segments, start_time, end_time, output_dir
        )
        
        if subtitle_file and os.path.exists(subtitle_file):
            with open(subtitle_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            dialogue_lines = [line for line in content.splitlines() if line.startswith('Dialogue:')]
            print(f"✅ Subtitle file created: {len(content)} chars, {len(dialogue_lines)} dialogues")
            
            if dialogue_lines:
                print(f"📝 Sample dialogue: {dialogue_lines[0][:80]}...")
                
                print(f"\n🎬 STEP 2: TEST FFMPEG COMMAND")
                print("-" * 40)
                
                # Test the exact FFmpeg command that would be used
                test_clip_path = "uploads/clips/SUBTITLE_TEST_RECREATION.mp4"
                
                # Get the original video path
                cursor.execute('SELECT file_path FROM videos WHERE id = ?', (video_id,))
                video_result = cursor.fetchone()
                if not video_result:
                    print("❌ Original video not found!")
                    return
                
                original_video_path = video_result[0]
                print(f"📹 Original video: {original_video_path}")
                
                if not os.path.exists(original_video_path):
                    print(f"❌ Original video file not found: {original_video_path}")
                    return
                
                # Build FFmpeg command (same as in video_processor.py)
                escaped_subtitle_path = subtitle_file.replace('\\', '\\\\').replace(':', '\\:').replace("'", "\\'")
                
                cmd = [
                    'ffmpeg',
                    '-i', original_video_path,
                    '-ss', str(start_time),
                    '-t', str(end_time - start_time),
                    '-c:v', 'libx264',
                    '-c:a', 'aac',
                    '-preset', 'fast',
                    '-crf', '23',
                    '-vf', f"crop=ih*9/16:ih,ass='{escaped_subtitle_path}'",
                    '-y', test_clip_path
                ]
                
                print(f"🎯 FFmpeg command:")
                print(f"   {' '.join(cmd[:10])}...")
                print(f"   Video filters: crop=ih*9/16:ih,ass='{subtitle_file}'")
                
                # Execute FFmpeg
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
                
                print(f"📊 FFmpeg result: Return code {result.returncode}")
                
                if result.returncode == 0 and os.path.exists(test_clip_path):
                    test_size = os.path.getsize(test_clip_path)
                    print(f"✅ Test clip created: {test_size:,} bytes")
                    
                    # Compare sizes
                    size_diff = test_size - file_size
                    print(f"📊 Size comparison:")
                    print(f"   Original clip: {file_size:,} bytes")
                    print(f"   Test clip:     {test_size:,} bytes")
                    print(f"   Difference:    {size_diff:+,} bytes")
                    
                    if abs(size_diff) < 100000:  # Within 100KB
                        print("✅ Sizes are similar - subtitles should be embedded!")
                    else:
                        print("⚠️  Size difference - might indicate subtitle issue")
                        
                else:
                    print(f"❌ FFmpeg failed!")
                    print(f"STDERR: {result.stderr[:500]}...")
                
                # Clean up
                if os.path.exists(subtitle_file):
                    os.remove(subtitle_file)
                    
            else:
                print("❌ NO DIALOGUE EVENTS IN SUBTITLE FILE!")
        else:
            print("❌ Failed to create subtitle file!")
            
    except json.JSONDecodeError as e:
        print(f"❌ Failed to parse transcript: {e}")
    
    conn.close()
    
    print(f"\n🎯 CONCLUSION:")
    print("=" * 60)
    print("If the test clip was created successfully with similar size,")
    print("then subtitles ARE being embedded, but might not be visible due to:")
    print("1. Video player not supporting ASS subtitles")
    print("2. Subtitle positioning/styling issues")
    print("3. Subtitle timing problems")
    print("4. Font or color visibility issues")

if __name__ == "__main__":
    asyncio.run(test_actual_clip_subtitles())
