"""
WebSocket Manager for real-time communication with frontend
"""

from fastapi import WebSocket
from typing import Dict, List
import json
import logging

logger = logging.getLogger(__name__)

class WebSocketManager:
    """Manages WebSocket connections for real-time updates"""
    
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
    
    async def connect(self, websocket: WebSocket, client_id: str):
        """Accept a new WebSocket connection"""
        await websocket.accept()
        self.active_connections[client_id] = websocket
        logger.info(f"Client {client_id} connected. Total connections: {len(self.active_connections)}")
        
        # Send welcome message
        await self.send_personal_message(
            json.dumps({
                "type": "connection",
                "message": "Connected to Viral Clip Generator",
                "client_id": client_id
            }),
            client_id
        )
    
    def disconnect(self, client_id: str):
        """Remove a WebSocket connection"""
        if client_id in self.active_connections:
            del self.active_connections[client_id]
            logger.info(f"Client {client_id} disconnected. Total connections: {len(self.active_connections)}")
    
    async def send_personal_message(self, message: str, client_id: str):
        """Send a message to a specific client"""
        if client_id in self.active_connections:
            try:
                await self.active_connections[client_id].send_text(message)
            except Exception as e:
                logger.error(f"Error sending message to {client_id}: {e}")
                # Remove disconnected client
                self.disconnect(client_id)
    
    async def broadcast(self, message: str):
        """Send a message to all connected clients"""
        disconnected_clients = []
        
        for client_id, websocket in self.active_connections.items():
            try:
                await websocket.send_text(message)
            except Exception as e:
                logger.error(f"Error broadcasting to {client_id}: {e}")
                disconnected_clients.append(client_id)
        
        # Remove disconnected clients
        for client_id in disconnected_clients:
            self.disconnect(client_id)
    
    async def send_processing_update(self, client_id: str, job_id: int, status: str, progress: float, message: str = ""):
        """Send processing update to specific client"""
        update = {
            "type": "processing_update",
            "job_id": job_id,
            "status": status,
            "progress": progress,
            "message": message,
            "timestamp": json.dumps({"$date": {"$numberLong": str(int(__import__("time").time() * 1000))}})
        }
        
        await self.send_personal_message(json.dumps(update), client_id)
    
    async def send_clip_generated(self, client_id: str, clip_data: dict):
        """Send notification when a new clip is generated"""
        notification = {
            "type": "clip_generated",
            "clip": clip_data,
            "timestamp": json.dumps({"$date": {"$numberLong": str(int(__import__("time").time() * 1000))}})
        }

        await self.send_personal_message(json.dumps(notification), client_id)

    async def send_processing_complete(self, client_id: str, video_id: int, clips_count: int):
        """Send notification when processing is complete"""
        notification = {
            "type": "processing_complete",
            "data": {
                "video_id": video_id,
                "clips_count": clips_count
            },
            "timestamp": json.dumps({"$date": {"$numberLong": str(int(__import__("time").time() * 1000))}})
        }

        await self.send_personal_message(json.dumps(notification), client_id)
    
    async def send_error(self, client_id: str, error_message: str, error_type: str = "general"):
        """Send error notification to client"""
        error = {
            "type": "error",
            "error_type": error_type,
            "message": error_message,
            "timestamp": json.dumps({"$date": {"$numberLong": str(int(__import__("time").time() * 1000))}})
        }
        
        await self.send_personal_message(json.dumps(error), client_id)
    
    def get_connection_count(self) -> int:
        """Get the number of active connections"""
        return len(self.active_connections)
    
    def get_connected_clients(self) -> List[str]:
        """Get list of connected client IDs"""
        return list(self.active_connections.keys())
