import asyncio
import os
import sys
import subprocess
from pathlib import Path

# Add the app directory to the Python path
sys.path.append(str(Path(__file__).parent / "app"))

from app.services.video_processor import VideoProcessor

async def verify_subtitle_visibility():
    """Verify if subtitles are actually visible in generated clips"""
    
    print("🔍 VERIFYING SUBTITLE VISIBILITY IN GENERATED CLIPS")
    print("=" * 70)
    
    # Check if test video exists
    test_video_path = "uploads/videos/test-video.mp4"
    if not os.path.exists(test_video_path):
        print("❌ Test video not found!")
        return
    
    print(f"✅ Test video found: {test_video_path}")
    
    # Test with real transcript data
    test_segments = [
        {
            'start': 0.0,
            'end': 5.0,
            'text': 'The problem is today, it can be pushed in a way',
            'words': [
                {'word': 'The', 'start': 0.0, 'end': 0.5},
                {'word': 'problem', 'start': 0.5, 'end': 1.0},
                {'word': 'is', 'start': 1.0, 'end': 1.2},
                {'word': 'today,', 'start': 1.2, 'end': 1.8},
                {'word': 'it', 'start': 1.8, 'end': 2.0},
                {'word': 'can', 'start': 2.0, 'end': 2.3},
                {'word': 'be', 'start': 2.3, 'end': 2.5},
                {'word': 'pushed', 'start': 2.5, 'end': 3.0},
                {'word': 'in', 'start': 3.0, 'end': 3.2},
                {'word': 'a', 'start': 3.2, 'end': 3.3},
                {'word': 'way', 'start': 3.3, 'end': 3.7}
            ]
        },
        {
            'start': 3.7,
            'end': 5.7,
            'text': "that's so different than when we were kids.",
            'words': [
                {'word': "that's", 'start': 3.7, 'end': 4.0},
                {'word': 'so', 'start': 4.0, 'end': 4.2},
                {'word': 'different', 'start': 4.2, 'end': 4.8},
                {'word': 'than', 'start': 4.8, 'end': 5.0},
                {'word': 'when', 'start': 5.0, 'end': 5.2},
                {'word': 'we', 'start': 5.2, 'end': 5.3},
                {'word': 'were', 'start': 5.3, 'end': 5.5},
                {'word': 'kids.', 'start': 5.5, 'end': 5.7}
            ]
        }
    ]
    
    processor = VideoProcessor()
    
    print("\n🎬 STEP 1: CREATE CLIP WITHOUT SUBTITLES (CONTROL)")
    print("-" * 60)
    
    # Create clip without subtitles for comparison
    control_clip_path = "uploads/clips/NO_SUBTITLES_CONTROL.mp4"
    
    cmd_no_subs = [
        'ffmpeg',
        '-i', test_video_path,
        '-ss', '0',
        '-t', '6',
        '-c:v', 'libx264',
        '-c:a', 'aac',
        '-preset', 'fast',
        '-crf', '23',
        '-vf', 'crop=ih*9/16:ih',
        '-y', control_clip_path
    ]
    
    result = subprocess.run(cmd_no_subs, capture_output=True, text=True, timeout=60)
    
    if result.returncode == 0 and os.path.exists(control_clip_path):
        control_size = os.path.getsize(control_clip_path)
        print(f"✅ Control clip (no subtitles) created: {control_size} bytes")
    else:
        print("❌ Failed to create control clip")
        return
    
    print("\n🎬 STEP 2: CREATE CLIP WITH SUBTITLES USING PROCESSOR")
    print("-" * 60)
    
    # Create clip with subtitles using the processor
    subtitle_clip_path = "uploads/clips/WITH_SUBTITLES_TEST.mp4"
    
    success = await processor.extract_clip(
        test_video_path,
        0.0,
        6.0,
        subtitle_clip_path,
        crop_to_vertical=True,
        subtitle_segments=test_segments
    )
    
    if success and os.path.exists(subtitle_clip_path):
        subtitle_size = os.path.getsize(subtitle_clip_path)
        print(f"✅ Subtitle clip created: {subtitle_size} bytes")
        
        # Compare file sizes
        size_diff = subtitle_size - control_size
        size_diff_percent = (size_diff / control_size) * 100
        
        print(f"📊 File size comparison:")
        print(f"   Control (no subs): {control_size:,} bytes")
        print(f"   With subtitles:    {subtitle_size:,} bytes")
        print(f"   Difference:        {size_diff:+,} bytes ({size_diff_percent:+.1f}%)")
        
        if abs(size_diff_percent) > 5:
            print("✅ Significant size difference suggests subtitles are being processed")
        else:
            print("⚠️  Small size difference - subtitles might not be visible")
            
    else:
        print("❌ Failed to create subtitle clip")
        return
    
    print("\n🎬 STEP 3: CREATE CLIP WITH MAXIMUM VISIBILITY SUBTITLES")
    print("-" * 60)
    
    # Create a clip with extremely visible subtitles for final verification
    max_vis_clip_path = "uploads/clips/MAX_VISIBILITY_TEST.mp4"
    
    # Create super visible ASS file
    ass_content = """[Script Info]
Title: MAXIMUM VISIBILITY TEST
ScriptType: v4.00+
PlayResX: 404
PlayResY: 720

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: MaxVis,Arial Black,80,&H0000FFFF,&H000000FF,&H00000000,&HFF000000,1,0,0,0,100,100,0,0,1,10,5,2,20,20,80,1

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
Dialogue: 0,0:00:00.00,0:00:03.00,MaxVis,,0,0,0,,SUBTITLES SHOULD BE VISIBLE HERE
Dialogue: 0,0:00:03.00,0:00:06.00,MaxVis,,0,0,0,,THIS IS A VISIBILITY TEST
"""
    
    max_vis_ass_path = "uploads/clips/max_visibility.ass"
    with open(max_vis_ass_path, 'w', encoding='utf-8') as f:
        f.write(ass_content)
    
    escaped_ass_path = max_vis_ass_path.replace('\\', '\\\\').replace(':', '\\:').replace("'", "\\'")
    
    cmd_max_vis = [
        'ffmpeg',
        '-i', test_video_path,
        '-ss', '0',
        '-t', '6',
        '-c:v', 'libx264',
        '-c:a', 'aac',
        '-preset', 'fast',
        '-crf', '23',
        '-vf', f"crop=ih*9/16:ih,ass='{escaped_ass_path}'",
        '-y', max_vis_clip_path
    ]
    
    result = subprocess.run(cmd_max_vis, capture_output=True, text=True, timeout=60)
    
    if result.returncode == 0 and os.path.exists(max_vis_clip_path):
        max_vis_size = os.path.getsize(max_vis_clip_path)
        print(f"✅ Maximum visibility clip created: {max_vis_size} bytes")
        
        # Compare with control
        max_vis_diff = max_vis_size - control_size
        max_vis_diff_percent = (max_vis_diff / control_size) * 100
        
        print(f"📊 Max visibility comparison:")
        print(f"   Control:           {control_size:,} bytes")
        print(f"   Max visibility:    {max_vis_size:,} bytes")
        print(f"   Difference:        {max_vis_diff:+,} bytes ({max_vis_diff_percent:+.1f}%)")
        
    else:
        print("❌ Failed to create maximum visibility clip")
        print(f"FFmpeg error: {result.stderr}")
    
    print("\n🎉 SUBTITLE VISIBILITY VERIFICATION COMPLETE!")
    print("=" * 70)
    print("📺 Generated test clips:")
    print(f"   1. Control (no subs):     {control_clip_path}")
    print(f"   2. Processor subtitles:   {subtitle_clip_path}")
    print(f"   3. Max visibility test:   {max_vis_clip_path}")
    print("\n💡 Open these videos to visually verify subtitle visibility!")
    
    # Clean up
    if os.path.exists(max_vis_ass_path):
        os.remove(max_vis_ass_path)

if __name__ == "__main__":
    asyncio.run(verify_subtitle_visibility())
