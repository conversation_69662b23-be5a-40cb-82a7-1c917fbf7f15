#!/usr/bin/env python3

import asyncio
import os
import sys
import json
from pathlib import Path

# Add the app directory to Python path
sys.path.append(str(Path(__file__).parent / 'app'))

async def debug_subtitle_clip_mapping():
    """Debug the subtitle-to-clip mapping to find mismatches"""
    
    print("🔍 DEBUGGING SUBTITLE-TO-CLIP MAPPING")
    print("=" * 70)
    
    # Get the most recent video and its clips from database
    import sqlite3
    conn = sqlite3.connect('viral_clips.db')
    cursor = conn.cursor()
    
    # Get the most recent video with transcript data
    cursor.execute('''
        SELECT id, file_path, transcript_with_timestamps 
        FROM videos 
        WHERE transcript_with_timestamps IS NOT NULL 
        ORDER BY id DESC 
        LIMIT 1
    ''')
    
    result = cursor.fetchone()
    if not result:
        print("❌ No videos with transcript data found")
        return
    
    video_id, video_path, transcript_json = result
    transcript_data = json.loads(transcript_json)
    all_transcript_segments = transcript_data.get('segments', [])
    
    print(f"✅ Video {video_id}: {video_path}")
    print(f"✅ Total transcript segments: {len(all_transcript_segments)}")
    
    # Get the clips for this video
    cursor.execute('''
        SELECT id, start_time, end_time, duration, title, file_path
        FROM clips 
        WHERE video_id = ? 
        ORDER BY start_time
    ''', (video_id,))
    
    clips = cursor.fetchall()
    if not clips:
        print("❌ No clips found for this video")
        return
    
    print(f"✅ Found {len(clips)} clips to analyze")
    
    print(f"\n🎬 ANALYZING SUBTITLE-TO-CLIP MAPPING")
    print("-" * 70)
    
    for i, (clip_id, start_time, end_time, duration, title, file_path) in enumerate(clips, 1):
        print(f"\n📹 CLIP {i}: {title}")
        print(f"   ID: {clip_id}")
        print(f"   Time: {start_time:.1f}s - {end_time:.1f}s ({duration:.1f}s)")
        print(f"   File: {file_path}")
        
        # EXACT same logic as in ai_processor.py
        clip_start = start_time
        clip_end = end_time
        
        clip_subtitle_segments = []
        for transcript_seg in all_transcript_segments:
            seg_start = transcript_seg.get('start', 0)
            seg_end = transcript_seg.get('end', 0)
            
            # Check if segment overlaps with clip timeframe
            if not (seg_end <= clip_start or seg_start >= clip_end):
                clip_subtitle_segments.append(transcript_seg)
        
        print(f"   📝 Found {len(clip_subtitle_segments)} overlapping subtitle segments")
        
        if not clip_subtitle_segments:
            print(f"   ❌ NO SUBTITLE SEGMENTS FOUND!")
            print(f"   🔍 This explains why clip {i} has no subtitles!")
            
            # Find the closest segments
            closest_before = None
            closest_after = None
            
            for transcript_seg in all_transcript_segments:
                seg_start = transcript_seg.get('start', 0)
                seg_end = transcript_seg.get('end', 0)
                
                if seg_end <= clip_start:
                    if not closest_before or seg_start > closest_before['start']:
                        closest_before = transcript_seg
                elif seg_start >= clip_end:
                    if not closest_after or seg_start < closest_after['start']:
                        closest_after = transcript_seg
            
            if closest_before:
                print(f"   📍 Closest segment BEFORE: {closest_before['start']:.1f}s-{closest_before['end']:.1f}s")
                print(f"      Text: '{closest_before['text'][:50]}...'")
                gap_before = clip_start - closest_before['end']
                print(f"      Gap: {gap_before:.1f}s")
            
            if closest_after:
                print(f"   📍 Closest segment AFTER: {closest_after['start']:.1f}s-{closest_after['end']:.1f}s")
                print(f"      Text: '{closest_after['text'][:50]}...'")
                gap_after = closest_after['start'] - clip_end
                print(f"      Gap: {gap_after:.1f}s")
            
            continue
        
        # Show the subtitle segments that SHOULD be in this clip
        print(f"   📄 Subtitle segments for this clip:")
        for j, seg in enumerate(clip_subtitle_segments[:3]):  # Show first 3
            original_start = seg['start']
            original_end = seg['end']
            adjusted_start = max(0, original_start - clip_start)
            adjusted_end = min(clip_end - clip_start, original_end - clip_start)
            
            print(f"      {j+1}. Original: {original_start:.1f}s-{original_end:.1f}s")
            print(f"         Adjusted: {adjusted_start:.1f}s-{adjusted_end:.1f}s")
            print(f"         Text: '{seg['text'][:40]}...'")
        
        if len(clip_subtitle_segments) > 3:
            print(f"      ... and {len(clip_subtitle_segments) - 3} more segments")
        
        # Check if the clip file actually exists
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            print(f"   ✅ Clip file exists: {file_size:,} bytes")
            
            # Larger file size usually indicates subtitles are embedded
            if file_size > 500000:  # > 500KB
                print(f"   ✅ File size suggests subtitles might be embedded")
            else:
                print(f"   ⚠️  Small file size - subtitles might not be embedded")
        else:
            print(f"   ❌ Clip file does not exist!")
    
    conn.close()
    
    print(f"\n🎯 DIAGNOSIS")
    print("=" * 70)
    print("🔍 Check the output above for clips with 'NO SUBTITLE SEGMENTS FOUND!'")
    print("🔍 These clips will have no subtitles because no transcript segments overlap")
    print()
    print("💡 POSSIBLE ISSUES:")
    print("   1. Transcript timing doesn't match video timing")
    print("   2. Clips are generated in silent/music-only sections")
    print("   3. Transcript segments have timing gaps")
    print("   4. Viral moment detection picks non-speech segments")
    print()
    print("🎬 SOLUTION:")
    print("   - Ensure clips are generated around actual speech segments")
    print("   - Verify transcript timing accuracy")
    print("   - Check for gaps in transcript data")

if __name__ == "__main__":
    asyncio.run(debug_subtitle_clip_mapping())
