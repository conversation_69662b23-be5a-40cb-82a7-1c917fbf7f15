#!/usr/bin/env python3

import asyncio
import os
import sys
import subprocess
from pathlib import Path

# Add the app directory to Python path
sys.path.append(str(Path(__file__).parent / 'app'))

from app.services.video_processor import VideoProcessor

async def test_embedded_subtitle_fix():
    """Test the embedded subtitle fix"""
    
    print("🔧 TESTING EMBEDDED SUBTITLE FIX")
    print("=" * 70)
    
    # Get a video file
    videos_dir = Path("uploads/videos")
    video_files = list(videos_dir.glob("*.mp4"))
    
    if not video_files:
        print("❌ No video files found")
        return
    
    test_video = str(video_files[0])
    print(f"✅ Using video: {test_video}")
    
    # Create test segments with word timing
    test_segments = [
        {
            'start': 2.0,
            'end': 5.0,
            'text': 'Testing embedded subtitles',
            'words': [
                {'word': ' Testing', 'start': 2.0, 'end': 2.8, 'confidence': 0.9},
                {'word': ' embedded', 'start': 2.8, 'end': 3.5, 'confidence': 0.95},
                {'word': ' subtitles', 'start': 3.5, 'end': 5.0, 'confidence': 0.9}
            ]
        }
    ]
    
    processor = VideoProcessor()
    
    print(f"\n🎬 TESTING EMBEDDED SUBTITLE CREATION")
    print("-" * 70)
    
    # Create test clip with embedded subtitles
    clip_start = 10.0
    clip_end = 20.0
    test_clip_path = "uploads/clips/TEST_EMBEDDED_SUBTITLES.mp4"
    
    print(f"📹 Creating clip: {clip_start}s - {clip_end}s")
    print(f"📝 With {len(test_segments)} subtitle segments")
    
    success = await processor.extract_clip(
        test_video,
        clip_start,
        clip_end,
        test_clip_path,
        crop_to_vertical=True,
        subtitle_segments=test_segments
    )
    
    if success and os.path.exists(test_clip_path):
        file_size = os.path.getsize(test_clip_path)
        print(f"✅ Clip created successfully: {file_size:,} bytes")
        
        # Test 1: Check for subtitle streams
        print(f"\n🔍 CHECKING FOR EMBEDDED SUBTITLE STREAMS")
        print("-" * 50)
        
        try:
            cmd = [
                "ffprobe", 
                "-v", "quiet", 
                "-print_format", "json", 
                "-show_streams", 
                test_clip_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                import json
                data = json.loads(result.stdout)
                streams = data.get('streams', [])
                
                video_streams = [s for s in streams if s.get('codec_type') == 'video']
                audio_streams = [s for s in streams if s.get('codec_type') == 'audio']
                subtitle_streams = [s for s in streams if s.get('codec_type') == 'subtitle']
                
                print(f"📺 Video streams: {len(video_streams)}")
                print(f"🔊 Audio streams: {len(audio_streams)}")
                print(f"📝 Subtitle streams: {len(subtitle_streams)}")
                
                if subtitle_streams:
                    print(f"🎉 SUCCESS! SUBTITLES ARE EMBEDDED AS STREAMS!")
                    for i, sub_stream in enumerate(subtitle_streams):
                        codec = sub_stream.get('codec_name', 'unknown')
                        print(f"   Stream {i}: {codec}")
                else:
                    print(f"❌ NO SUBTITLE STREAMS FOUND")
                    print(f"   Subtitles might be burned-in or missing")
            else:
                print(f"❌ FFprobe failed: {result.stderr}")
                
        except Exception as e:
            print(f"❌ Error checking streams: {e}")
        
        # Test 2: Try to extract subtitles
        print(f"\n📤 TESTING SUBTITLE EXTRACTION")
        print("-" * 50)
        
        try:
            subtitle_output = "extracted_test_subtitles.ass"
            
            cmd = [
                "ffmpeg", 
                "-i", test_clip_path,
                "-map", "0:s:0",  # Extract first subtitle stream
                "-c", "copy",
                "-y",  # Overwrite
                subtitle_output
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0 and os.path.exists(subtitle_output):
                subtitle_size = os.path.getsize(subtitle_output)
                print(f"✅ EXTRACTED SUBTITLES: {subtitle_size} bytes")
                
                # Read and show sample content
                with open(subtitle_output, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                lines = content.splitlines()
                dialogue_lines = [line for line in lines if line.startswith('Dialogue:')]
                
                print(f"📝 Dialogue lines: {len(dialogue_lines)}")
                if dialogue_lines:
                    print(f"📄 Sample: {dialogue_lines[0][:80]}...")
                
                # Clean up
                os.remove(subtitle_output)
            else:
                print(f"❌ EXTRACTION FAILED")
                if result.stderr:
                    print(f"   Error: {result.stderr[:100]}...")
                
        except Exception as e:
            print(f"❌ Error extracting subtitles: {e}")
        
        # Clean up test clip
        # os.remove(test_clip_path)
        
    else:
        print(f"❌ Clip creation failed")
    
    print(f"\n🎯 SUMMARY")
    print("=" * 70)
    print("🔧 Testing the fix for embedded subtitles")
    print("✅ If subtitle streams are found, the fix is working!")
    print("❌ If no subtitle streams, we need to adjust the approach")
    print("🎬 Check the results above for embedded subtitle streams")

if __name__ == "__main__":
    asyncio.run(test_embedded_subtitle_fix())
