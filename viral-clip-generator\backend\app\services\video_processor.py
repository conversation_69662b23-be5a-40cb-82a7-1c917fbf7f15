"""
Video processing service - simplified demo version
"""

import asyncio
import subprocess
import json
import os
import uuid
from pathlib import Path
from typing import Dict, Any, List, Tuple
import logging

from ..core.config import settings

logger = logging.getLogger(__name__)

class VideoProcessor:
    """Simplified video processing for demo"""

    def __init__(self):
        self.temp_dir = settings.UPLOAD_DIR / "temp"
        self.temp_dir.mkdir(exist_ok=True)
    
    async def get_video_metadata(self, video_path: str) -> Dict[str, Any]:
        """Extract video metadata - simplified demo version"""
        try:
            # Simulate metadata extraction
            await asyncio.sleep(0.5)

            # Return mock metadata
            metadata = {
                'duration': 120.0,  # 2 minutes
                'size': 50000000,   # 50MB
                'format': 'mp4',
                'bitrate': 2000000, # 2Mbps
                'width': 1920,
                'height': 1080,
                'fps': 30.0,
                'video_codec': 'h264',
                'pixel_format': 'yuv420p',
                'audio_codec': 'aac',
                'sample_rate': 44100,
                'channels': 2
            }

            return metadata

        except Exception as e:
            logger.error(f"Error extracting metadata from {video_path}: {str(e)}")
            return {}
    
    async def extract_clip(
        self,
        video_path: str,
        start_time: float,
        end_time: float,
        output_path: str,
        crop_to_vertical: bool = True,
        subtitle_segments: list = None
    ) -> bool:
        """Extract a clip from video using ffmpeg with embedded subtitles"""
        try:
            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)

            # Create subtitle file if segments provided
            subtitle_file = None
            if subtitle_segments:
                logger.info(f"📝 Creating subtitle file for {len(subtitle_segments)} segments")
                subtitle_file = await self._create_subtitle_file(subtitle_segments, start_time, end_time, output_dir)

                # DEBUG: Check if subtitle file was created and log its content
                if subtitle_file and os.path.exists(subtitle_file):
                    file_size = os.path.getsize(subtitle_file)
                    logger.info(f"✅ Subtitle file created successfully: {subtitle_file} ({file_size} bytes)")

                    # Log first few lines of subtitle file for debugging
                    try:
                        with open(subtitle_file, 'r', encoding='utf-8') as f:
                            content = f.read()[:500]  # First 500 chars
                            logger.info(f"📄 Subtitle file content preview: {content}")
                    except Exception as e:
                        logger.error(f"❌ Could not read subtitle file: {e}")
                else:
                    logger.error(f"❌ Subtitle file was not created or is None: {subtitle_file}")

            # Build ffmpeg command for clip extraction
            cmd = [
                'ffmpeg',
                '-i', video_path,
                '-ss', str(start_time),
                '-t', str(end_time - start_time),
                '-c:v', 'libx264',
                '-c:a', 'aac',
                '-preset', 'fast',
                '-crf', '23'
            ]

            # Build video filter chain
            video_filters = []

            # Add vertical crop if requested (for TikTok/Instagram format)
            if crop_to_vertical:
                video_filters.append('crop=ih*9/16:ih')

            # Add INCREDIBLE ANIMATED subtitles if available
            if subtitle_file and os.path.exists(subtitle_file):
                # Escape the subtitle file path for FFmpeg
                escaped_subtitle_path = subtitle_file.replace('\\', '\\\\').replace(':', '\\:').replace("'", "\\'")

                # Use ASS subtitles for AMAZING animations
                if subtitle_file.endswith('.ass'):
                    subtitle_filter = f"ass='{escaped_subtitle_path}'"
                else:
                    # Fallback to basic subtitles
                    subtitle_filter = f"subtitles='{escaped_subtitle_path}'"

                video_filters.append(subtitle_filter)

            # Apply video filters if any
            if video_filters:
                cmd.extend(['-vf', ','.join(video_filters)])

            # Add output file and overwrite flag
            cmd.extend(['-y', output_path])

            # Execute ffmpeg command
            import subprocess

            # DEBUG: Log the full command
            logger.info(f"🎬 Executing FFmpeg command: {' '.join(cmd)}")
            if subtitle_file:
                logger.info(f"📝 Using subtitle file: {subtitle_file}")

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=120  # 2 minute timeout for subtitle processing
            )

            # DEBUG: Always log FFmpeg output
            logger.info(f"📊 FFmpeg return code: {result.returncode}")
            if result.stdout:
                logger.info(f"📤 FFmpeg stdout: {result.stdout[:500]}")
            if result.stderr:
                logger.info(f"📤 FFmpeg stderr: {result.stderr[:500]}")

            # Clean up subtitle file AFTER logging
            if subtitle_file and os.path.exists(subtitle_file):
                logger.info(f"🧹 Cleaning up subtitle file: {subtitle_file}")
                os.remove(subtitle_file)

            if result.returncode == 0 and os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                logger.info(f"✅ Clip extracted successfully with subtitles: {output_path} ({file_size} bytes)")
                return True
            else:
                logger.error(f"❌ FFmpeg failed with return code {result.returncode}")
                logger.error(f"❌ FFmpeg stderr: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            logger.error(f"FFmpeg timeout extracting clip: {output_path}")
            return False
        except Exception as e:
            logger.error(f"Error extracting clip: {str(e)}")
            return False

    async def _create_subtitle_file(self, segments: list, clip_start: float, clip_end: float, output_dir: Path) -> str:
        """Create INCREDIBLE ANIMATED subtitle file with word-by-word timing"""
        try:
            subtitle_filename = f"temp_animated_subtitles_{uuid.uuid4().hex[:8]}.ass"
            subtitle_path = output_dir / subtitle_filename

            # Filter segments that fall within the clip timeframe
            clip_segments = []
            for segment in segments:
                seg_start = segment.get('start', 0)
                seg_end = segment.get('end', 0)

                # Check if segment overlaps with clip
                if not (seg_end <= clip_start or seg_start >= clip_end):
                    # Adjust timing relative to clip start
                    adjusted_start = max(0, seg_start - clip_start)
                    adjusted_end = min(clip_end - clip_start, seg_end - clip_start)

                    if adjusted_end > adjusted_start:
                        clip_segments.append({
                            'start': adjusted_start,
                            'end': adjusted_end,
                            'text': segment.get('text', '').strip()
                        })

            # Generate INCREDIBLE ASS subtitle content with animations
            ass_content = self._generate_animated_ass_content(clip_segments)

            # Write ASS file
            with open(subtitle_path, 'w', encoding='utf-8') as f:
                f.write(ass_content)

            logger.info(f"Created ANIMATED subtitle file: {subtitle_path} with {len(clip_segments)} segments")
            return str(subtitle_path)

        except Exception as e:
            logger.error(f"Error creating animated subtitle file: {str(e)}")
            return None

    def _generate_animated_ass_content(self, segments: list) -> str:
        """Generate INCREDIBLE ASS subtitle content with FULL SENTENCE + WORD HIGHLIGHTING"""

        # ASS header with MASSIVE VIRAL STYLING - INCREDIBLE ANIMATIONS
        # FIXED for vertical video (9:16 aspect ratio)
        ass_header = """[Script Info]
Title: VIRAL SENTENCE HIGHLIGHTING SUBTITLES
ScriptType: v4.00+
PlayResX: 404
PlayResY: 720

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: ViralMain,Arial Black,48,&H00FFFFFF,&H000000FF,&H00000000,&H80000000,1,0,0,0,100,100,2,0,1,4,3,2,20,20,50,1

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
"""

        events = []

        for segment in segments:
            text = segment['text']
            start_time = segment['start']
            end_time = segment['end']
            duration = end_time - start_time

            # Use word-level timestamps if available, otherwise split text
            segment_words = segment.get('words', [])
            if segment_words:
                # Use actual word timestamps from Whisper
                words_with_timing = []
                for word_data in segment_words:
                    # Adjust word timing relative to segment start
                    word_start = word_data['start'] - start_time
                    word_end = word_data['end'] - start_time
                    words_with_timing.append({
                        'word': word_data['word'].strip(),
                        'start': max(0, word_start),  # Ensure non-negative
                        'end': min(duration, word_end),  # Ensure within segment
                        'duration': word_end - word_start
                    })
                words = [w['word'] for w in words_with_timing]
            else:
                # Fallback: split text and estimate timing
                words = text.split()
                if not words:
                    continue
                time_per_word = duration / len(words)
                words_with_timing = []
                for i, word in enumerate(words):
                    word_start = i * time_per_word
                    word_end = (i + 1) * time_per_word
                    words_with_timing.append({
                        'word': word,
                        'start': word_start,
                        'end': word_end,
                        'duration': time_per_word
                    })

            if not words:
                continue

            # Split long sentences into multiple lines (max 3-4 words per line for mobile)
            lines = self._split_text_for_mobile(text, max_words_per_line=4)
            formatted_text = "\\N".join(lines)  # \\N creates line breaks in ASS

            # Create SINGLE KARAOKE effect with proper word highlighting
            start_ass = self._seconds_to_ass_time(start_time)
            end_ass = self._seconds_to_ass_time(end_time)

            # Build INCREDIBLY SMOOTH KARAOKE text with CREATIVE animations
            karaoke_text = ""

            for i, word_data in enumerate(words_with_timing):
                word = word_data['word']
                word_timing = int(word_data['duration'] * 100)  # Convert to centiseconds

                # Determine if this is a VIRAL word that needs special effects
                word_lower = word.lower().strip('.,!?;:')
                is_viral_word = word_lower in ["amazing", "incredible", "wow", "unbelievable", "insane", "crazy", "mind-blowing", "awesome", "fantastic"]
                is_exclamation = word.endswith(('!', '?'))

                # CREATIVE SMOOTH TIMING - longer, more gradual transitions
                smooth_start = word_timing // 6      # Start animation early
                smooth_mid1 = word_timing // 3       # First transition point
                smooth_mid2 = word_timing * 2 // 3   # Second transition point
                smooth_end = word_timing             # End of word

                if is_viral_word:
                    # VIRAL words - ULTRA SMOOTH wave-like animation with glow
                    karaoke_text += f"{{\\k{word_timing}" \
                                  f"\\t(0,{smooth_start},\\alpha&HFF&\\fscx80\\fscy80\\c&H00FFFF00&\\blur0)" \
                                  f"\\t({smooth_start},{smooth_mid1},\\alpha&H40&\\fscx110\\fscy110\\c&H0000FFFF&\\blur1)" \
                                  f"\\t({smooth_mid1},{smooth_mid2},\\alpha&H00&\\fscx125\\fscy125\\c&H00FF00FF&\\blur2)" \
                                  f"\\t({smooth_mid2},{smooth_end},\\alpha&H00&\\fscx100\\fscy100\\c&H00FFFFFF&\\blur0)" \
                                  f"}}{word}"
                elif is_exclamation:
                    # EXCLAMATIONS - Smooth elastic bounce effect
                    karaoke_text += f"{{\\k{word_timing}" \
                                  f"\\t(0,{smooth_start},\\alpha&HFF&\\fscx90\\fscy90\\c&H00FFFF00&)" \
                                  f"\\t({smooth_start},{smooth_mid1},\\alpha&H20&\\fscx120\\fscy120\\c&H00FF0000&)" \
                                  f"\\t({smooth_mid1},{smooth_mid2},\\alpha&H00&\\fscx105\\fscy105\\c&H00FFFF00&)" \
                                  f"\\t({smooth_mid2},{smooth_end},\\alpha&H00&\\fscx100\\fscy100\\c&H00FFFFFF&)" \
                                  f"}}{word}"
                elif i == 0:
                    # FIRST word - Ultra smooth fade-in with gentle scale
                    karaoke_text += f"{{\\k{word_timing}" \
                                  f"\\t(0,{smooth_mid1},\\alpha&HFF&\\fscx95\\fscy95\\c&H80FFFFFF&)" \
                                  f"\\t({smooth_mid1},{smooth_mid2},\\alpha&H40&\\fscx105\\fscy105\\c&H00FFFF00&)" \
                                  f"\\t({smooth_mid2},{smooth_end},\\alpha&H00&\\fscx100\\fscy100\\c&H00FFFFFF&)" \
                                  f"}}{word}"
                else:
                    # REGULAR words - Smooth wave-like highlight
                    karaoke_text += f"{{\\k{word_timing}" \
                                  f"\\t(0,{smooth_start},\\alpha&HFF&\\fscx98\\fscy98\\c&H80FFFFFF&)" \
                                  f"\\t({smooth_start},{smooth_mid2},\\alpha&H20&\\fscx108\\fscy108\\c&H00FFFF00&)" \
                                  f"\\t({smooth_mid2},{smooth_end},\\alpha&H00&\\fscx100\\fscy100\\c&H00FFFFFF&)" \
                                  f"}}{word}"

                # Add space between words (except last word)
                if i < len(words) - 1:
                    karaoke_text += " "

            # Add INCREDIBLY SMOOTH subtitle event with CREATIVE karaoke effects
            events.append(f"Dialogue: 0,{start_ass},{end_ass},ViralMain,,0,0,0,,{karaoke_text}")

        return ass_header + "\n".join(events)

    def _split_text_for_mobile(self, text: str, max_words_per_line: int = 4) -> list:
        """Split text into mobile-friendly lines"""
        words = text.split()
        lines = []
        current_line = []

        for word in words:
            current_line.append(word)
            if len(current_line) >= max_words_per_line:
                lines.append(" ".join(current_line))
                current_line = []

        if current_line:
            lines.append(" ".join(current_line))

        return lines

    def _seconds_to_ass_time(self, seconds: float) -> str:
        """Convert seconds to ASS time format (H:MM:SS.CC)"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        centiseconds = int((seconds % 1) * 100)
        return f"{hours}:{minutes:02d}:{secs:02d}.{centiseconds:02d}"

    def _seconds_to_srt_time(self, seconds: float) -> str:
        """Convert seconds to SRT time format (HH:MM:SS,mmm)"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        milliseconds = int((seconds % 1) * 1000)
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{milliseconds:03d}"

    def _split_subtitle_text(self, text: str, max_chars_per_line: int = 42) -> list:
        """Split subtitle text into multiple lines for better readability"""
        words = text.split()
        lines = []
        current_line = ""

        for word in words:
            if len(current_line + " " + word) <= max_chars_per_line:
                current_line = current_line + " " + word if current_line else word
            else:
                if current_line:
                    lines.append(current_line)
                current_line = word

        if current_line:
            lines.append(current_line)

        return lines
    
    async def detect_faces_and_center(self, video_path: str) -> List[Dict[str, Any]]:
        """Detect faces - simplified demo version"""
        try:
            # Simulate face detection
            await asyncio.sleep(1)

            # Return mock face data
            faces_data = [
                {
                    'frame': 0,
                    'timestamp': 0.0,
                    'x': 400,
                    'y': 200,
                    'width': 200,
                    'height': 200,
                    'center_x': 500,
                    'center_y': 300
                },
                {
                    'frame': 900,
                    'timestamp': 30.0,
                    'x': 450,
                    'y': 250,
                    'width': 180,
                    'height': 180,
                    'center_x': 540,
                    'center_y': 340
                }
            ]

            return faces_data

        except Exception as e:
            logger.error(f"Error detecting faces: {str(e)}")
            return []
    
    async def generate_thumbnail(self, video_path: str, timestamp: float, output_path: str) -> bool:
        """Generate a thumbnail from video using ffmpeg"""
        try:
            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)

            # Build ffmpeg command for thumbnail generation
            cmd = [
                'ffmpeg',
                '-i', video_path,
                '-ss', str(timestamp),
                '-vframes', '1',  # Extract 1 frame
                '-q:v', '2',  # High quality
                '-y',  # Overwrite output file
                output_path
            ]

            # Execute ffmpeg command
            import subprocess
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=30  # 30 second timeout
            )

            if result.returncode == 0 and os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                logger.info(f"Thumbnail generated successfully: {output_path} ({file_size} bytes)")
                return True
            else:
                logger.error(f"FFmpeg thumbnail generation failed: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            logger.error(f"FFmpeg timeout generating thumbnail: {output_path}")
            return False
        except Exception as e:
            logger.error(f"Error generating thumbnail: {str(e)}")
            return False
    
    async def extract_audio(self, video_path: str, output_path: str) -> bool:
        """Extract audio from video using ffmpeg"""
        try:
            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)

            # Build ffmpeg command for audio extraction
            cmd = [
                'ffmpeg',
                '-i', video_path,
                '-vn',  # No video
                '-acodec', 'pcm_s16le',  # PCM 16-bit for Whisper
                '-ar', '16000',  # 16kHz sample rate for Whisper
                '-ac', '1',  # Mono
                '-y',  # Overwrite output file
                output_path
            ]

            # Execute ffmpeg command
            import subprocess
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=120  # 2 minute timeout for audio extraction
            )

            if result.returncode == 0 and os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                logger.info(f"Audio extracted successfully: {output_path} ({file_size} bytes)")
                return True
            else:
                logger.error(f"FFmpeg audio extraction failed: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            logger.error(f"FFmpeg timeout extracting audio: {output_path}")
            return False
        except Exception as e:
            logger.error(f"Error extracting audio: {str(e)}")
            return False
    
    async def add_background_music(
        self,
        video_path: str,
        music_path: str,
        output_path: str,
        music_volume: float = 0.3
    ) -> bool:
        """Add background music - simplified demo version"""
        try:
            # Simulate adding background music
            await asyncio.sleep(1.5)

            # Create a dummy output file
            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)

            with open(output_path, 'w') as f:
                f.write("dummy video with music")

            return True

        except Exception as e:
            logger.error(f"Error adding background music: {str(e)}")
            return False
