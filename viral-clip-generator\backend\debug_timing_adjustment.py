#!/usr/bin/env python3

import asyncio
import os
import sys
import json
from pathlib import Path

# Add the app directory to Python path
sys.path.append(str(Path(__file__).parent / 'app'))

from app.services.video_processor import VideoProcessor

async def debug_timing_adjustment():
    """Debug timing adjustment in SRT generation"""
    
    print("🔍 DEBUGGING TIMING ADJUSTMENT IN SRT GENERATION")
    print("=" * 70)
    
    # Get real transcript data
    import sqlite3
    conn = sqlite3.connect('viral_clips.db')
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT transcript_with_timestamps 
        FROM videos 
        WHERE transcript_with_timestamps IS NOT NULL 
        ORDER BY id DESC 
        LIMIT 1
    ''')
    
    result = cursor.fetchone()
    if not result:
        print("❌ No transcript data found")
        return
    
    transcript_data = json.loads(result[0])
    segments = transcript_data.get('segments', [])
    
    print(f"✅ Found {len(segments)} transcript segments")
    
    # Test with a specific clip timeframe
    clip_start = 25.0
    clip_end = 40.0
    
    print(f"\n📹 TESTING CLIP: {clip_start}s - {clip_end}s")
    print("-" * 50)
    
    # Filter segments using the EXACT same logic as in video_processor.py
    clip_segments = []
    for segment in segments:
        seg_start = segment.get('start', 0)
        seg_end = segment.get('end', 0)

        # Check if segment overlaps with clip
        if not (seg_end <= clip_start or seg_start >= clip_end):
            # Adjust timing relative to clip start
            adjusted_start = max(0, seg_start - clip_start)
            adjusted_end = min(clip_end - clip_start, seg_end - clip_start)

            if adjusted_end > adjusted_start:
                # Also adjust word timings if they exist
                adjusted_words = []
                if 'words' in segment and segment['words']:
                    for word_data in segment['words']:
                        word_start = word_data.get('start', seg_start)
                        word_end = word_data.get('end', seg_end)
                        
                        # Adjust word timing relative to clip start
                        adj_word_start = max(0, word_start - clip_start)
                        adj_word_end = min(clip_end - clip_start, word_end - clip_start)
                        
                        # Only include words that fall within the clip timeframe
                        if adj_word_end > adj_word_start and adj_word_start < (clip_end - clip_start):
                            adjusted_words.append({
                                'word': word_data.get('word', ''),
                                'start': adj_word_start,
                                'end': adj_word_end,
                                'confidence': word_data.get('confidence', 1.0)
                            })
                
                clip_segments.append({
                    'start': adjusted_start,
                    'end': adjusted_end,
                    'text': segment.get('text', '').strip(),
                    'words': adjusted_words
                })
    
    print(f"📝 Found {len(clip_segments)} overlapping segments")
    
    if not clip_segments:
        print("❌ No segments found for this timeframe")
        return
    
    # Show the adjusted segments
    print(f"\n📄 ADJUSTED SEGMENTS:")
    print("-" * 30)
    for i, seg in enumerate(clip_segments[:3]):
        print(f"{i+1}. {seg['start']:.1f}s - {seg['end']:.1f}s: '{seg['text'][:50]}...'")
    
    # Create SRT file
    processor = VideoProcessor()
    
    print(f"\n📝 CREATING SRT FILE WITH ADJUSTED TIMING")
    print("-" * 50)
    
    output_dir = Path("uploads/clips")
    subtitle_file = await processor._create_subtitle_file(clip_segments, clip_start, clip_end, output_dir)
    
    if subtitle_file and os.path.exists(subtitle_file):
        print(f"✅ SRT file created: {subtitle_file}")
        
        # Read and display the SRT content
        with open(subtitle_file, 'r', encoding='utf-8') as f:
            srt_content = f.read()
        
        print(f"\n📄 SRT CONTENT:")
        print("-" * 30)
        print(srt_content[:500] + "..." if len(srt_content) > 500 else srt_content)
        print("-" * 30)
        
        # Check if content is valid
        if srt_content.strip():
            print(f"✅ SRT content is not empty")
            
            # Check for negative times or invalid format
            lines = srt_content.strip().split('\n')
            has_issues = False
            
            for line in lines:
                if '-->' in line:
                    if '-' in line.replace('-->', ''):  # Check for negative times
                        print(f"❌ Found negative time: {line}")
                        has_issues = True
            
            if not has_issues:
                print(f"✅ No timing issues found")
            
        else:
            print(f"❌ SRT content is empty!")
        
        # Keep file for inspection
        print(f"🔍 SRT file kept: {subtitle_file}")
        
    else:
        print(f"❌ SRT file creation failed")
    
    conn.close()

if __name__ == "__main__":
    asyncio.run(debug_timing_adjustment())
