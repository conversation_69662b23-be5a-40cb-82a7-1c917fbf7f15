"""
Configuration settings for the Viral Clip Generator API
"""

import os
from pathlib import Path
from typing import Optional
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    """Application settings"""
    
    # API Configuration
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "Viral Clip Generator"
    VERSION: str = "1.0.0"
    DESCRIPTION: str = "AI-powered viral video clip generation"
    
    # Server Configuration
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    DEBUG: bool = True
    
    # Database Configuration
    DATABASE_URL: str = "sqlite:///./viral_clips.db"
    
    # File Storage Configuration
    UPLOAD_DIR: Path = Path("uploads")
    MAX_FILE_SIZE: int = 2 * 1024 * 1024 * 1024  # 2GB
    ALLOWED_VIDEO_EXTENSIONS: list = [".mp4", ".avi", ".mov", ".mkv", ".webm"]
    
    # AI Model Configuration
    WHISPER_MODEL: str = "base"  # base, small, medium, large
    WHISPER_DEVICE: str = "cpu"  # cpu, cuda
    
    # LLM Configuration
    LLM_MODEL: str = "microsoft/DialoGPT-medium"  # Free alternative to GPT-4
    LLM_MAX_TOKENS: int = 1000
    
    # Video Processing Configuration
    OUTPUT_FORMAT: str = "mp4"
    OUTPUT_RESOLUTION: tuple = (1080, 1920)  # 9:16 aspect ratio
    OUTPUT_FPS: int = 30
    
    # Clip Generation Configuration
    MIN_CLIP_DURATION: int = 15  # seconds
    MAX_CLIP_DURATION: int = 60  # seconds
    MAX_CLIPS_PER_VIDEO: int = 10
    
    # Audio Configuration
    AUDIO_SAMPLE_RATE: int = 44100
    AUDIO_CHANNELS: int = 2
    
    # Redis Configuration (for task queue)
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # Security
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # CORS Configuration
    ALLOWED_ORIGINS: list = [
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:8000",
    ]
    
    # Logging Configuration
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    class Config:
        env_file = ".env"
        case_sensitive = True

# Create settings instance
settings = Settings()

# Ensure upload directories exist
def create_directories():
    """Create necessary directories"""
    directories = [
        settings.UPLOAD_DIR,
        settings.UPLOAD_DIR / "videos",
        settings.UPLOAD_DIR / "clips",
        settings.UPLOAD_DIR / "temp",
        settings.UPLOAD_DIR / "thumbnails",
        settings.UPLOAD_DIR / "subtitles",
        settings.UPLOAD_DIR / "audio",
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)

# Initialize directories on import
create_directories()
