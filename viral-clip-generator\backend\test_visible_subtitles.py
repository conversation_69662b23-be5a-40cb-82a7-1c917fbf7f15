import asyncio
import os
import sys
from pathlib import Path

# Add the app directory to the Python path
sys.path.append(str(Path(__file__).parent / "app"))

from app.services.video_processor import VideoProcessor

async def test_visible_subtitles():
    """Test with EXTREMELY VISIBLE subtitles to verify they're being burned in"""
    
    print("🔍 TESTING EXTREMELY VISIBLE SUBTITLES")
    print("=" * 60)
    
    # Check if test video exists
    test_video_path = "uploads/videos/test-video.mp4"
    if not os.path.exists(test_video_path):
        print("❌ Test video not found!")
        return
    
    print(f"✅ Test video found: {test_video_path}")
    
    # Create simple, highly visible test segments
    test_segments = [
        {
            'start': 0.0,
            'end': 5.0,
            'text': 'HELLO WORLD TEST',
            'words': [
                {'word': 'HELLO', 'start': 0.0, 'end': 2.5},
                {'word': 'WORLD', 'start': 2.5, 'end': 4.0},
                {'word': 'TEST', 'start': 4.0, 'end': 5.0}
            ]
        },
        {
            'start': 5.0,
            'end': 10.0,
            'text': 'SUBTITLES ARE WORKING',
            'words': [
                {'word': 'SUBTITLES', 'start': 5.0, 'end': 7.0},
                {'word': 'ARE', 'start': 7.0, 'end': 8.0},
                {'word': 'WORKING', 'start': 8.0, 'end': 10.0}
            ]
        }
    ]
    
    processor = VideoProcessor()
    
    print("\n🎨 STEP 1: CREATING SIMPLE VISIBLE SUBTITLE FILE")
    print("-" * 50)
    
    # Create subtitle file manually with MAXIMUM visibility
    output_dir = Path("uploads/clips")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Create a simple ASS file with MAXIMUM visibility
    subtitle_filename = "SUPER_VISIBLE_TEST.ass"
    subtitle_path = output_dir / subtitle_filename
    
    # Create EXTREMELY VISIBLE ASS content
    ass_content = """[Script Info]
Title: SUPER VISIBLE TEST SUBTITLES
ScriptType: v4.00+
PlayResX: 404
PlayResY: 720

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: SuperVisible,Arial Black,60,&H00FFFF00,&H000000FF,&H00000000,&H80000000,1,0,0,0,100,100,0,0,1,8,4,2,20,20,100,1

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
Dialogue: 0,0:00:00.00,0:00:05.00,SuperVisible,,0,0,0,,HELLO WORLD TEST
Dialogue: 0,0:00:05.00,0:00:10.00,SuperVisible,,0,0,0,,SUBTITLES ARE WORKING
"""
    
    with open(subtitle_path, 'w', encoding='utf-8') as f:
        f.write(ass_content)
    
    print(f"✅ Super visible subtitle file created: {subtitle_path}")
    print(f"📁 File size: {os.path.getsize(subtitle_path)} bytes")
    
    print("\n🎬 STEP 2: CREATING VIDEO WITH SUPER VISIBLE SUBTITLES")
    print("-" * 50)
    
    clip_path = "uploads/clips/SUPER_VISIBLE_SUBTITLE_TEST.mp4"
    
    # Build FFmpeg command manually for maximum control
    import subprocess
    
    escaped_subtitle_path = str(subtitle_path).replace('\\', '\\\\').replace(':', '\\:').replace("'", "\\'")
    
    cmd = [
        'ffmpeg',
        '-i', test_video_path,
        '-ss', '0',
        '-t', '10',
        '-c:v', 'libx264',
        '-c:a', 'aac',
        '-preset', 'fast',
        '-crf', '23',
        '-vf', f"crop=ih*9/16:ih,ass='{escaped_subtitle_path}'",
        '-y', clip_path
    ]
    
    print("🎯 FFMPEG COMMAND:")
    print(" ".join(cmd))
    
    result = subprocess.run(
        cmd,
        capture_output=True,
        text=True,
        timeout=60
    )
    
    print(f"\n📊 RETURN CODE: {result.returncode}")
    
    if result.returncode == 0 and os.path.exists(clip_path):
        file_size = os.path.getsize(clip_path)
        print(f"✅ SUPER VISIBLE subtitle video created: {file_size} bytes")
        print(f"📁 Location: {clip_path}")
        
        # Also test with the processor method
        print("\n🔧 STEP 3: TESTING WITH PROCESSOR METHOD")
        print("-" * 50)
        
        method_clip_path = "uploads/clips/PROCESSOR_VISIBLE_TEST.mp4"
        success = await processor.extract_clip(
            test_video_path,
            0.0,
            10.0,
            method_clip_path,
            crop_to_vertical=True,
            subtitle_segments=test_segments
        )
        
        if success and os.path.exists(method_clip_path):
            method_file_size = os.path.getsize(method_clip_path)
            print(f"✅ Processor method video created: {method_file_size} bytes")
            print(f"📁 Location: {method_clip_path}")
        else:
            print("❌ Processor method failed")
            
    else:
        print("❌ FFmpeg failed!")
        print(f"STDERR: {result.stderr}")
    
    print("\n🎉 VISIBLE SUBTITLE TEST COMPLETE!")
    print("=" * 60)
    print("📺 Check the generated videos to see if subtitles are visible:")
    print(f"   - {clip_path}")
    print(f"   - uploads/clips/PROCESSOR_VISIBLE_TEST.mp4")

if __name__ == "__main__":
    asyncio.run(test_visible_subtitles())
