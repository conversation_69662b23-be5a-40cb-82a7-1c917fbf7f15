#!/usr/bin/env python3
"""
Test MULTIPLE viral clip generation with SMOOTH animations
"""

import asyncio
import os
import sys
import logging
from pathlib import Path

# Add the app directory to Python path
sys.path.append('.')

from app.services.ai_processor import AIProcessor
from app.services.websocket_manager import WebSocketManager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_multiple_viral_clips():
    """Test generating MULTIPLE viral clips with SMOOTH animations"""
    
    print("🎬 TESTING MULTIPLE VIRAL CLIP GENERATION")
    print("=" * 60)
    
    # Check if test video exists
    test_video_path = "uploads/videos/test-video.mp4"
    if not os.path.exists(test_video_path):
        print(f"❌ Test video not found: {test_video_path}")
        return
    
    print(f"✅ Test video found: {test_video_path}")
    
    # Initialize components
    websocket_manager = WebSocketManager()
    ai_processor = AIProcessor(websocket_manager)
    
    # Create a mock video object
    class MockVideo:
        def __init__(self):
            self.id = 1
            self.file_path = test_video_path
            self.transcript = None
            self.transcript_with_timestamps = None
            self.status = "uploaded"
    
    video = MockVideo()
    
    print("\n🎤 STEP 1: EXTRACTING AUDIO AND TRANSCRIBING")
    print("-" * 50)
    
    # Extract audio
    audio_path = await ai_processor._extract_audio(video)
    if not audio_path or not os.path.exists(audio_path):
        print("❌ Audio extraction failed")
        return
    
    print(f"✅ Audio extracted: {audio_path}")
    
    # Mock database session
    class MockDB:
        async def commit(self):
            pass
    
    db = MockDB()
    
    # Get REAL transcription
    transcript_data = await ai_processor._transcribe_audio(audio_path, video, db)
    
    if not transcript_data or not transcript_data.get('segments'):
        print("❌ Transcription failed")
        return
    
    print(f"✅ Transcription complete!")
    print(f"📝 Text: {transcript_data['text'][:100]}...")
    print(f"⏱️  Duration: {transcript_data['duration']:.1f}s")
    print(f"📄 Segments: {len(transcript_data['segments'])}")
    
    print("\n🔥 STEP 2: DETECTING MULTIPLE VIRAL MOMENTS")
    print("-" * 50)
    
    # Detect viral moments (this should find multiple clips)
    viral_segments = await ai_processor._detect_viral_moments(transcript_data)
    
    print(f"🎯 DETECTED {len(viral_segments)} VIRAL MOMENTS:")
    print("-" * 40)
    
    for i, segment in enumerate(viral_segments):
        print(f"  📹 Clip {i+1}: {segment['start_time']:.1f}s - {segment['end_time']:.1f}s")
        print(f"     Duration: {segment['duration']:.1f}s")
        print(f"     Score: {segment['viral_score']:.3f}")
        print(f"     Text: {segment['text'][:60]}...")
        print()
    
    print("\n🎬 STEP 3: GENERATING MULTIPLE CLIPS WITH SMOOTH ANIMATIONS")
    print("-" * 60)
    
    # Generate all clips
    from app.services.video_processor import VideoProcessor
    processor = VideoProcessor()
    
    generated_clips = []
    
    # Get all transcript segments for subtitle extraction
    all_transcript_segments = transcript_data.get("segments", [])

    for i, segment in enumerate(viral_segments[:5]):  # Generate first 5 clips
        print(f"\n📹 GENERATING CLIP {i+1}/{min(len(viral_segments), 5)}")
        print(f"   ⏱️  Time: {segment['start_time']:.1f}s - {segment['end_time']:.1f}s")
        print(f"   📝 Text: {segment['text'][:80]}...")

        clip_path = f"uploads/clips/VIRAL_CLIP_{i+1}_SMOOTH.mp4"

        # CORRECTLY extract subtitle segments for this specific clip timeframe
        clip_start = segment["start_time"]
        clip_end = segment["end_time"]

        clip_subtitle_segments = []
        for transcript_seg in all_transcript_segments:
            seg_start = transcript_seg.get('start', 0)
            seg_end = transcript_seg.get('end', 0)

            # Check if segment overlaps with clip timeframe
            if not (seg_end <= clip_start or seg_start >= clip_end):
                clip_subtitle_segments.append(transcript_seg)

        print(f"   📝 Subtitle segments: {len(clip_subtitle_segments)} segments for this clip")

        success = await processor.extract_clip(
            test_video_path,
            segment["start_time"],
            segment["end_time"],
            clip_path,
            crop_to_vertical=True,
            subtitle_segments=clip_subtitle_segments
        )
        
        if success and os.path.exists(clip_path):
            file_size = os.path.getsize(clip_path)
            print(f"   ✅ Generated: {file_size} bytes")
            generated_clips.append({
                'path': clip_path,
                'size': file_size,
                'duration': segment['duration'],
                'score': segment['viral_score']
            })
        else:
            print(f"   ❌ Failed to generate clip {i+1}")
    
    # Clean up
    if os.path.exists(audio_path):
        os.remove(audio_path)
    
    print("\n🎉 MULTIPLE VIRAL CLIPS GENERATION COMPLETE!")
    print("=" * 60)
    print(f"✅ GENERATED {len(generated_clips)} VIRAL CLIPS")
    print("✅ Each clip has SMOOTH animated subtitles")
    print("✅ Perfect audio-subtitle sync")
    print("✅ Optimal 30-60 second durations")
    
    print("\n📊 CLIP SUMMARY:")
    print("-" * 30)
    total_size = 0
    for i, clip in enumerate(generated_clips):
        print(f"  Clip {i+1}: {clip['size']/1024/1024:.1f}MB, {clip['duration']:.1f}s, Score: {clip['score']:.3f}")
        total_size += clip['size']
    
    print(f"\n📁 Total size: {total_size/1024/1024:.1f}MB")
    print(f"🎯 Average viral score: {sum(c['score'] for c in generated_clips)/len(generated_clips):.3f}")

if __name__ == "__main__":
    asyncio.run(test_multiple_viral_clips())
