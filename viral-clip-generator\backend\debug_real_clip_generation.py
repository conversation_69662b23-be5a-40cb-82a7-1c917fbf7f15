import asyncio
import os
import sys
import json
import sqlite3
from pathlib import Path

# Add the app directory to the Python path
sys.path.append(str(Path(__file__).parent / "app"))

from app.services.ai_processor import AIProcessor
from app.services.video_processor import VideoProcessor

async def debug_real_clip_generation():
    """Debug the actual clip generation process to see why subtitles aren't working"""
    
    print("🔍 DEBUGGING REAL CLIP GENERATION PROCESS")
    print("=" * 70)
    
    # Get the most recent video from database
    conn = sqlite3.connect('viral_clips.db')
    cursor = conn.cursor()
    
    cursor.execute('SELECT * FROM videos ORDER BY id DESC LIMIT 1')
    video_data = cursor.fetchone()
    
    if not video_data:
        print("❌ No videos found in database!")
        return
    
    video_id = video_data[0]
    video_path = video_data[2]

    print(f"✅ Found video: ID {video_id}")
    print(f"📁 Path: {video_path}")

    # Get the correct column for transcript_with_timestamps
    print(f"📊 Video data columns: {len(video_data)}")

    # Find transcript_with_timestamps - it should be a JSON string
    transcript_with_timestamps = None
    for i, data in enumerate(video_data):
        if isinstance(data, str) and (data.startswith('{') or data.startswith('[')):
            try:
                json.loads(data)
                transcript_with_timestamps = data
                print(f"✅ Found transcript data at column {i}")
                break
            except:
                continue

    if not transcript_with_timestamps:
        print("❌ No transcript data found!")
        print("Available data:", [type(d).__name__ for d in video_data])
        return
    
    # Parse transcript data
    try:
        transcript_data = json.loads(transcript_with_timestamps)
        all_segments = transcript_data.get('segments', [])
        print(f"📝 Transcript segments: {len(all_segments)}")
        
        # Show first few segments
        print("\n📋 SAMPLE TRANSCRIPT SEGMENTS:")
        print("-" * 50)
        for i, seg in enumerate(all_segments[:3]):
            print(f"  {i+1}: {seg.get('start', 0):.1f}s - {seg.get('end', 0):.1f}s")
            print(f"      Text: '{seg.get('text', '')}'")
            print(f"      Keys: {list(seg.keys())}")
        
    except json.JSONDecodeError as e:
        print(f"❌ Failed to parse transcript: {e}")
        return
    
    # Get existing clips for this video
    cursor.execute('SELECT * FROM clips WHERE video_id = ? ORDER BY id DESC LIMIT 1', (video_id,))
    clip_data = cursor.fetchone()
    
    if clip_data:
        clip_start = clip_data[5]  # start_time
        clip_end = clip_data[6]    # end_time
        clip_path = clip_data[3]   # file_path
        
        print(f"\n🎬 EXISTING CLIP FOUND:")
        print(f"   Start: {clip_start:.1f}s - End: {clip_end:.1f}s")
        print(f"   Path: {clip_path}")
        print(f"   Has subtitles: {clip_data[17]}")  # has_subtitles
        
        # Extract subtitle segments for this clip
        clip_subtitle_segments = []
        for transcript_seg in all_segments:
            seg_start = transcript_seg.get('start', 0)
            seg_end = transcript_seg.get('end', 0)
            
            # Check if segment overlaps with clip timeframe
            if not (seg_end <= clip_start or seg_start >= clip_end):
                clip_subtitle_segments.append(transcript_seg)
        
        print(f"   Subtitle segments: {len(clip_subtitle_segments)}")
        
        # Show the subtitle segments that should be in this clip
        print("\n📝 SUBTITLE SEGMENTS FOR THIS CLIP:")
        print("-" * 50)
        for i, seg in enumerate(clip_subtitle_segments):
            print(f"  {i+1}: {seg.get('start', 0):.1f}s - {seg.get('end', 0):.1f}s")
            print(f"      Text: '{seg.get('text', '')}'")
        
        # Test creating a new clip with the same parameters
        print(f"\n🔧 TESTING CLIP RECREATION WITH SUBTITLES")
        print("-" * 50)
        
        processor = VideoProcessor()
        test_clip_path = "uploads/clips/RECREATED_WITH_SUBTITLES.mp4"
        
        success = await processor.extract_clip(
            video_path,
            clip_start,
            clip_end,
            test_clip_path,
            crop_to_vertical=True,
            subtitle_segments=clip_subtitle_segments
        )
        
        if success and os.path.exists(test_clip_path):
            file_size = os.path.getsize(test_clip_path)
            print(f"✅ Recreated clip with subtitles: {file_size} bytes")
            print(f"📁 Location: {test_clip_path}")
            
            # Compare with original
            original_size = os.path.getsize(clip_path) if os.path.exists(clip_path) else 0
            if original_size > 0:
                size_diff = file_size - original_size
                size_diff_percent = (size_diff / original_size) * 100
                print(f"📊 Size comparison:")
                print(f"   Original: {original_size:,} bytes")
                print(f"   With subs: {file_size:,} bytes")
                print(f"   Difference: {size_diff:+,} bytes ({size_diff_percent:+.1f}%)")
        else:
            print("❌ Failed to recreate clip with subtitles")
    
    conn.close()
    
    print("\n🎉 REAL CLIP GENERATION DEBUG COMPLETE!")
    print("=" * 70)

if __name__ == "__main__":
    asyncio.run(debug_real_clip_generation())
