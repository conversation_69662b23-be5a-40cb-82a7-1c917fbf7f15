#!/usr/bin/env python3
"""
DEEP DEBUG - Check every step of subtitle processing
"""

import asyncio
import os
import sys
import logging
import subprocess
from pathlib import Path

# Add the app directory to Python path
sys.path.append('.')

from app.services.video_processor import VideoProcessor

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

async def deep_debug_subtitles():
    """DEEP DEBUG every step of subtitle processing"""
    
    print("🔍 DEEP DEBUGGING SUBTITLE PROCESSING")
    print("=" * 60)
    
    # Check if test video exists
    test_video_path = "uploads/videos/test-video.mp4"
    if not os.path.exists(test_video_path):
        print(f"❌ Test video not found: {test_video_path}")
        return
    
    print(f"✅ Test video found: {test_video_path}")
    
    # Create simple test subtitle segments
    test_segments = [
        {"start": 0.0, "end": 5.0, "text": "FIRST SUBTITLE LINE"},
        {"start": 5.0, "end": 10.0, "text": "SECOND SUBTITLE LINE"},
        {"start": 10.0, "end": 15.0, "text": "THIRD SUBTITLE LINE"}
    ]
    
    processor = VideoProcessor()
    
    print("\n🎬 STEP 1: MANUAL SUBTITLE FILE CREATION")
    print("-" * 50)
    
    # Create subtitle file manually
    output_dir = Path("uploads/clips")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    subtitle_file = await processor._create_subtitle_file(test_segments, 0.0, 15.0, output_dir)
    
    if not subtitle_file or not os.path.exists(subtitle_file):
        print("❌ FAILED to create subtitle file!")
        return
    
    print(f"✅ Subtitle file created: {subtitle_file}")
    print(f"📁 File size: {os.path.getsize(subtitle_file)} bytes")
    
    # Read and verify subtitle content
    with open(subtitle_file, 'r', encoding='utf-8') as f:
        subtitle_content = f.read()
    
    print(f"📝 Subtitle content length: {len(subtitle_content)} characters")
    print("\n📄 FULL SUBTITLE CONTENT:")
    print("-" * 30)
    print(subtitle_content)
    print("-" * 30)
    
    print("\n🔧 STEP 2: MANUAL FFMPEG COMMAND CONSTRUCTION")
    print("-" * 50)
    
    # Build the EXACT command that extract_clip would use
    clip_path = "uploads/clips/DEEP_DEBUG_TEST.mp4"
    
    # Remove existing file
    if os.path.exists(clip_path):
        os.remove(clip_path)
    
    # Build command step by step
    cmd = [
        'ffmpeg',
        '-i', test_video_path,
        '-ss', '0',
        '-t', '15',
        '-c:v', 'libx264',
        '-c:a', 'aac',
        '-preset', 'fast',
        '-crf', '23'
    ]
    
    # Add video filters
    video_filters = []
    
    # Add crop filter
    video_filters.append('crop=ih*9/16:ih')
    
    # Add subtitle filter
    escaped_subtitle_path = subtitle_file.replace('\\', '\\\\').replace(':', '\\:').replace("'", "\\'")
    subtitle_filter = f"ass='{escaped_subtitle_path}'"
    video_filters.append(subtitle_filter)
    
    # Add filters to command
    cmd.extend(['-vf', ','.join(video_filters)])
    cmd.extend(['-y', clip_path])
    
    print("🎯 FINAL FFMPEG COMMAND:")
    print(" ".join(cmd))
    
    print("\n⚡ STEP 3: EXECUTING FFMPEG WITH DETAILED OUTPUT")
    print("-" * 50)
    
    # Execute with maximum verbosity
    result = subprocess.run(
        cmd,
        capture_output=True,
        text=True,
        timeout=60
    )
    
    print(f"📊 RETURN CODE: {result.returncode}")
    print(f"📤 STDOUT LENGTH: {len(result.stdout)} chars")
    print(f"📤 STDERR LENGTH: {len(result.stderr)} chars")
    
    if result.stdout:
        print("\n📤 STDOUT:")
        print(result.stdout)
    
    if result.stderr:
        print("\n📤 STDERR:")
        print(result.stderr)
    
    print("\n🔍 STEP 4: VERIFYING OUTPUT VIDEO")
    print("-" * 50)
    
    if os.path.exists(clip_path):
        file_size = os.path.getsize(clip_path)
        print(f"✅ Output video created: {file_size} bytes")
        
        # Check if file is valid video
        probe_cmd = ['ffprobe', '-v', 'error', '-show_format', '-show_streams', clip_path]
        probe_result = subprocess.run(probe_cmd, capture_output=True, text=True)
        
        if probe_result.returncode == 0:
            print("✅ Video file is valid")
            print(f"📊 Probe output length: {len(probe_result.stdout)} chars")
        else:
            print("❌ Video file is INVALID!")
            print(f"❌ Probe error: {probe_result.stderr}")
    else:
        print("❌ NO OUTPUT VIDEO CREATED!")
    
    print("\n🔍 STEP 5: TESTING SUBTITLE FILTER SEPARATELY")
    print("-" * 50)
    
    # Test just the subtitle filter without crop
    test_clip_path = "uploads/clips/SUBTITLE_ONLY_TEST.mp4"
    
    if os.path.exists(test_clip_path):
        os.remove(test_clip_path)
    
    subtitle_only_cmd = [
        'ffmpeg',
        '-i', test_video_path,
        '-ss', '0',
        '-t', '15',
        '-c:v', 'libx264',
        '-c:a', 'aac',
        '-preset', 'fast',
        '-crf', '23',
        '-vf', subtitle_filter,
        '-y', test_clip_path
    ]
    
    print("🎯 SUBTITLE-ONLY COMMAND:")
    print(" ".join(subtitle_only_cmd))
    
    subtitle_result = subprocess.run(
        subtitle_only_cmd,
        capture_output=True,
        text=True,
        timeout=60
    )
    
    print(f"📊 SUBTITLE-ONLY RETURN CODE: {subtitle_result.returncode}")
    
    if os.path.exists(test_clip_path):
        file_size = os.path.getsize(test_clip_path)
        print(f"✅ Subtitle-only video created: {file_size} bytes")
    else:
        print("❌ Subtitle-only video FAILED!")
        print(f"❌ Error: {subtitle_result.stderr}")
    
    print("\n🔍 STEP 6: CHECKING ACTUAL extract_clip METHOD")
    print("-" * 50)
    
    # Test the actual extract_clip method
    method_clip_path = "uploads/clips/METHOD_TEST.mp4"
    
    if os.path.exists(method_clip_path):
        os.remove(method_clip_path)
    
    print("🎯 Testing actual extract_clip method...")
    
    success = await processor.extract_clip(
        test_video_path,
        0.0,  # start_time
        15.0,  # end_time
        method_clip_path,
        crop_to_vertical=True,
        subtitle_segments=test_segments
    )
    
    print(f"📊 extract_clip returned: {success}")
    
    if os.path.exists(method_clip_path):
        file_size = os.path.getsize(method_clip_path)
        print(f"✅ Method test video created: {file_size} bytes")
    else:
        print("❌ Method test video FAILED!")
    
    # Keep subtitle file for inspection
    print(f"\n📁 Subtitle file kept for inspection: {subtitle_file}")
    
    print("\n🎉 DEEP DEBUG COMPLETE!")
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(deep_debug_subtitles())
