#!/usr/bin/env python3

import asyncio
import os
import sys
import subprocess
from pathlib import Path

# Add the app directory to Python path
sys.path.append(str(Path(__file__).parent / 'app'))

async def debug_ffmpeg_command():
    """Debug the exact FFmpeg command being generated"""
    
    print("🔍 DEBUGGING FFMPEG COMMAND")
    print("=" * 70)
    
    # Get video and subtitle files
    videos_dir = Path("uploads/videos")
    video_files = list(videos_dir.glob("*.mp4"))
    
    clips_dir = Path("uploads/clips")
    srt_files = list(clips_dir.glob("temp_subtitles_*.srt"))
    
    if not video_files:
        print("❌ No video files found")
        return
        
    if not srt_files:
        print("❌ No SRT files found - run debug_srt_generation.py first")
        return
    
    video_path = str(video_files[0])
    subtitle_file = str(srt_files[0])
    
    print(f"✅ Video: {video_path}")
    print(f"✅ Subtitle: {subtitle_file}")
    
    # Build the exact same command as in video_processor.py
    start_time = 10.0
    end_time = 20.0
    output_path = "uploads/clips/DEBUG_TEST.mp4"
    
    cmd = [
        'ffmpeg',
        '-i', video_path
    ]
    
    # Add subtitle file as second input
    cmd.extend(['-i', subtitle_file])

    # Add timing and encoding parameters
    cmd.extend([
        '-ss', str(start_time),
        '-t', str(end_time - start_time),
        '-c:v', 'libx264',
        '-c:a', 'aac',
        '-preset', 'fast',
        '-crf', '23'
    ])

    # Add vertical crop
    cmd.extend(['-vf', 'crop=ih*9/16:ih'])

    # Map streams and set subtitle codec
    cmd.extend(['-map', '0:v', '-map', '0:a', '-map', '1:s'])
    cmd.extend(['-c:s', 'mov_text'])  # MP4-compatible subtitle format

    # Add output file and overwrite flag
    cmd.extend(['-y', output_path])
    
    print(f"\n🎯 FFMPEG COMMAND:")
    print("-" * 50)
    print(" ".join(cmd))
    
    print(f"\n⚡ EXECUTING FFMPEG:")
    print("-" * 50)
    
    try:
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=60
        )
        
        print(f"📊 Return code: {result.returncode}")
        
        if result.stdout:
            print(f"📤 STDOUT:")
            print(result.stdout[:500])
        
        if result.stderr:
            print(f"📤 STDERR:")
            print(result.stderr[:1000])
        
        if result.returncode == 0 and os.path.exists(output_path):
            file_size = os.path.getsize(output_path)
            print(f"\n✅ SUCCESS! File created: {file_size:,} bytes")
            
            # Check for subtitle streams
            print(f"\n🔍 CHECKING SUBTITLE STREAMS:")
            
            probe_cmd = [
                "ffprobe", 
                "-v", "quiet", 
                "-print_format", "json", 
                "-show_streams", 
                output_path
            ]
            
            probe_result = subprocess.run(probe_cmd, capture_output=True, text=True, timeout=30)
            
            if probe_result.returncode == 0:
                import json
                data = json.loads(probe_result.stdout)
                streams = data.get('streams', [])
                
                subtitle_streams = [s for s in streams if s.get('codec_type') == 'subtitle']
                
                if subtitle_streams:
                    print(f"🎉 SUBTITLE STREAMS FOUND: {len(subtitle_streams)}")
                    for i, sub_stream in enumerate(subtitle_streams):
                        codec = sub_stream.get('codec_name', 'unknown')
                        print(f"   Stream {i}: {codec}")
                else:
                    print(f"❌ No subtitle streams found")
            
        else:
            print(f"\n❌ FAILED!")
            
    except subprocess.TimeoutExpired:
        print(f"❌ FFmpeg timeout")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    asyncio.run(debug_ffmpeg_command())
