#!/usr/bin/env python3

import asyncio
import os
import sys
import json
from pathlib import Path

# Add the app directory to Python path
sys.path.append(str(Path(__file__).parent / 'app'))

async def debug_srt_generation_detailed():
    """Debug SRT generation in detail"""
    
    print("🔍 DEBUGGING SRT GENERATION IN DETAIL")
    print("=" * 70)
    
    # Test segments (same as from previous debug)
    test_segments = [
        {
            'start': 0.0,
            'end': 0.3,
            'text': "He was just, he was my friend's sister",
            'words': []
        },
        {
            'start': 0.3,
            'end': 2.4,
            'text': "or my friend's brother, whoops, <PERSON><PERSON>",
            'words': []
        },
        {
            'start': 2.4,
            'end': 4.5,
            'text': "I'll follow my friend's brother",
            'words': []
        }
    ]
    
    print(f"📝 Testing with {len(test_segments)} segments")
    
    # Manually implement the SRT generation logic
    def _seconds_to_srt_time(seconds: float) -> str:
        """Convert seconds to SRT time format (HH:MM:SS,mmm)"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        milliseconds = int((seconds % 1) * 1000)
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{milliseconds:03d}"
    
    def _generate_srt_content(segments: list) -> str:
        """Generate SRT subtitle content"""
        print(f"\n🔧 GENERATING SRT CONTENT")
        print("-" * 30)
        
        srt_lines = []
        subtitle_index = 1
        
        for i, segment in enumerate(segments):
            start_time = segment.get('start', 0)
            end_time = segment.get('end', 0)
            text = segment.get('text', '').strip()
            
            print(f"Segment {i+1}:")
            print(f"  start_time: {start_time}")
            print(f"  end_time: {end_time}")
            print(f"  text: '{text}'")
            print(f"  text length: {len(text)}")
            print(f"  text empty: {not text}")
            
            if not text:
                print(f"  ❌ SKIPPING - no text")
                continue
            
            # Convert seconds to SRT time format (HH:MM:SS,mmm)
            start_srt = _seconds_to_srt_time(start_time)
            end_srt = _seconds_to_srt_time(end_time)
            
            print(f"  start_srt: {start_srt}")
            print(f"  end_srt: {end_srt}")
            
            # Add SRT subtitle entry
            srt_lines.append(str(subtitle_index))
            srt_lines.append(f"{start_srt} --> {end_srt}")
            srt_lines.append(text)
            srt_lines.append("")  # Empty line between subtitles
            
            print(f"  ✅ ADDED to SRT")
            subtitle_index += 1
        
        result = "\n".join(srt_lines)
        print(f"\n📄 FINAL SRT CONTENT ({len(result)} chars):")
        print("-" * 30)
        print(result)
        print("-" * 30)
        
        return result
    
    # Test the SRT generation
    srt_content = _generate_srt_content(test_segments)
    
    # Write to file
    output_file = "uploads/clips/DEBUG_SRT_MANUAL.srt"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(srt_content)
    
    print(f"\n✅ Manual SRT file created: {output_file}")
    print(f"📊 File size: {os.path.getsize(output_file)} bytes")
    
    # Now test with the actual video processor
    print(f"\n🎬 TESTING WITH VIDEO PROCESSOR")
    print("-" * 50)
    
    from app.services.video_processor import VideoProcessor
    processor = VideoProcessor()
    
    output_dir = Path("uploads/clips")
    subtitle_file = await processor._create_subtitle_file(test_segments, 0.0, 10.0, output_dir)
    
    if subtitle_file and os.path.exists(subtitle_file):
        with open(subtitle_file, 'r', encoding='utf-8') as f:
            processor_content = f.read()
        
        print(f"✅ Processor SRT file: {subtitle_file}")
        print(f"📊 File size: {os.path.getsize(subtitle_file)} bytes")
        print(f"📄 Content length: {len(processor_content)} chars")
        
        if processor_content == srt_content:
            print(f"✅ Content matches manual generation")
        else:
            print(f"❌ Content differs from manual generation")
            print(f"Manual: {len(srt_content)} chars")
            print(f"Processor: {len(processor_content)} chars")
    else:
        print(f"❌ Processor SRT creation failed")

if __name__ == "__main__":
    asyncio.run(debug_srt_generation_detailed())
