"use client";

import { useState, useCallback, useEffect } from "react";
import { motion } from "framer-motion";
import { useDropzone } from "react-dropzone";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import {
  Upload,
  Video,
  FileVideo,
  CheckCircle,
  AlertCircle,
  Loader2,
  Play,
  Download
} from "lucide-react";
import { toast } from "sonner";
import { apiClient, wsManager } from "@/lib/utils";

interface UploadedVideo {
  id: number;
  name: string;
  size: number;
  duration?: number;
  status: "uploading" | "processing" | "completed" | "error";
  progress: number;
  currentStep?: string;
  clips?: Array<{
    id: number;
    title: string;
    description?: string;
    duration: number;
    start_time: number;
    end_time: number;
    viral_score: number;
    sentiment?: string;
    emotion?: string;
    has_subtitles: boolean;
    has_background_music: boolean;
    thumbnail_url?: string;
    download_url: string;
    created_at: string;
  }>;
}

export function VideoUpload() {
  const [videos, setVideos] = useState<UploadedVideo[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Load existing videos on mount
  useEffect(() => {
    const loadExistingVideos = async () => {
      try {
        const videosData = await apiClient.getAllVideos();

        // Convert backend video format to frontend format and load clips
        const formattedVideos = await Promise.all(
          videosData.videos.map(async (video: any) => {
            let clips = [];

            // If video has clips, fetch them
            if (video.clips_count > 0) {
              try {
                const clipsData = await apiClient.getVideoClips(video.id);
                clips = clipsData.clips || [];
              } catch (error) {
                console.error(`Error fetching clips for video ${video.id}:`, error);
              }
            }

            return {
              id: video.id,
              file: null, // No file object for existing videos
              name: video.filename,
              size: video.size || 0,
              status: clips.length > 0 ? "completed" : (video.status || "uploaded"),
              progress: clips.length > 0 ? 100 : 0,
              currentStep: clips.length > 0 ? "Completed" : "Ready",
              clips: clips
            };
          })
        );

        setVideos(formattedVideos);
      } catch (error) {
        console.error('Error loading existing videos:', error);
        toast.error("Failed to load existing videos");
      } finally {
        setIsLoading(false);
      }
    };

    loadExistingVideos();
  }, []);

  // Setup WebSocket connection for real-time updates
  useEffect(() => {
    const handleWebSocketMessage = (data: any) => {
      console.log('WebSocket message:', data);

      if (data.type === 'processing_update') {
        const { video_id, progress, current_step, status } = data.data;

        setVideos(prev => prev.map(v =>
          v.id === video_id ? {
            ...v,
            progress: progress || v.progress,
            currentStep: current_step || v.currentStep,
            status: status || v.status
          } : v
        ));
      }

      if (data.type === 'processing_complete') {
        const { video_id } = data.data;

        // Fetch the completed clips
        fetchVideoClips(video_id);
      }
    };

    wsManager.connect(handleWebSocketMessage, (error) => {
      console.error('WebSocket error:', error);
    });

    return () => {
      wsManager.disconnect();
    };
  }, []);

  const fetchVideoClips = async (videoId: number) => {
    try {
      const clipsData = await apiClient.getVideoClips(videoId);

      setVideos(prev => prev.map(v =>
        v.id === videoId ? {
          ...v,
          status: "completed",
          progress: 100,
          clips: clipsData.clips || []
        } : v
      ));

      toast.success(`Generated ${clipsData.clips?.length || 0} viral clips!`);
    } catch (error) {
      console.error('Error fetching clips:', error);
      setVideos(prev => prev.map(v =>
        v.id === videoId ? { ...v, status: "error" } : v
      ));
      toast.error("Failed to fetch generated clips");
    }
  };

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith("video/")) {
      toast.error("Please upload a video file");
      return;
    }

    // Validate file size (2GB limit)
    if (file.size > 2 * 1024 * 1024 * 1024) {
      toast.error("File size must be less than 2GB");
      return;
    }

    setIsUploading(true);

    try {
      // Upload video to backend
      toast.info("Uploading video...");
      const uploadResult = await apiClient.uploadVideo(file);

      const newVideo: UploadedVideo = {
        id: uploadResult.video_id,
        name: uploadResult.filename,
        size: uploadResult.size,
        duration: uploadResult.duration,
        status: "processing",
        progress: 0,
        currentStep: "Starting AI processing..."
      };

      setVideos(prev => [...prev, newVideo]);
      toast.success("Video uploaded successfully!");

      // Start processing
      await apiClient.startProcessing(uploadResult.video_id, wsManager.getClientId());
      toast.info("AI processing started...");

    } catch (error: any) {
      console.error('Upload error:', error);
      toast.error(error.message || "Upload failed. Please try again.");
    } finally {
      setIsUploading(false);
    }
  }, []);

  const downloadClip = async (videoId: number, clipId: number, filename: string) => {
    try {
      const response = await fetch(`http://localhost:8000/api/clips/download/${clipId}`);

      if (!response.ok) {
        throw new Error('Download failed');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast.success('Clip downloaded successfully!');
    } catch (error) {
      console.error('Download error:', error);
      toast.error('Failed to download clip');
    }
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'video/*': ['.mp4', '.mov', '.avi', '.mkv', '.webm']
    },
    multiple: false,
    disabled: isUploading
  });

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getStatusIcon = (status: UploadedVideo['status']) => {
    switch (status) {
      case "uploading":
      case "processing":
        return <Loader2 className="w-5 h-5 animate-spin text-blue-500" />;
      case "completed":
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case "error":
        return <AlertCircle className="w-5 h-5 text-red-500" />;
    }
  };

  const getStatusText = (video: UploadedVideo) => {
    switch (video.status) {
      case "uploading":
        return "Uploading...";
      case "processing":
        return video.currentStep || "Processing with AI...";
      case "completed":
        return `Generated ${video.clips?.length || 0} clips`;
      case "error":
        return "Processing failed";
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Upload Area */}
      <Card className="border-2 border-dashed transition-colors hover:border-primary/50">
        <CardContent className="p-8">
          <div
            {...getRootProps()}
            className={`text-center cursor-pointer transition-colors ${
              isDragActive ? "text-primary" : "text-muted-foreground"
            }`}
          >
            <input {...getInputProps()} />
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="space-y-4"
            >
              <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
                <Upload className="w-8 h-8 text-primary" />
              </div>
              <div>
                <h3 className="text-xl font-semibold mb-2">
                  {isDragActive ? "Drop your video here" : "Upload your video"}
                </h3>
                <p className="text-muted-foreground mb-4">
                  Drag and drop a video file, or click to browse
                </p>
                <div className="flex flex-wrap justify-center gap-2 text-sm text-muted-foreground">
                  <Badge variant="secondary">MP4</Badge>
                  <Badge variant="secondary">MOV</Badge>
                  <Badge variant="secondary">AVI</Badge>
                  <Badge variant="secondary">MKV</Badge>
                  <Badge variant="secondary">WebM</Badge>
                </div>
                <p className="text-xs text-muted-foreground mt-2">
                  Maximum file size: 2GB
                </p>
              </div>
            </motion.div>
          </div>
        </CardContent>
      </Card>

      {/* Video List */}
      {videos.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Your Videos</h3>
          {videos.map((video) => (
            <Card key={video.id}>
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-muted rounded-lg flex items-center justify-center flex-shrink-0">
                    <FileVideo className="w-6 h-6 text-muted-foreground" />
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium truncate">{video.name}</h4>
                      {getStatusIcon(video.status)}
                    </div>
                    
                    <div className="flex items-center gap-4 text-sm text-muted-foreground mb-3">
                      <span>{formatFileSize(video.size)}</span>
                      <span>{getStatusText(video)}</span>
                    </div>

                    {(video.status === "uploading" || video.status === "processing") && (
                      <div className="space-y-2">
                        <Progress value={video.progress} className="mb-1" />
                        {video.currentStep && (
                          <p className="text-xs text-muted-foreground">{video.currentStep}</p>
                        )}
                      </div>
                    )}

                    {/* Generated Clips */}
                    {video.status === "completed" && video.clips && (
                      <div className="space-y-3">
                        <h5 className="font-medium text-sm">Generated Viral Clips</h5>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                          {video.clips.map((clip) => (
                            <div key={clip.id} className="border rounded-lg p-3 space-y-2">
                              <div className="aspect-video bg-muted rounded flex items-center justify-center">
                                <Video className="w-8 h-8 text-muted-foreground" />
                              </div>
                              <div>
                                <h6 className="font-medium text-sm">
                                  {clip.title || `${clip.start_time.toFixed(1)}s - ${clip.end_time.toFixed(1)}s`}
                                </h6>
                                {clip.description && (
                                  <p className="text-xs text-muted-foreground mb-1 line-clamp-2">
                                    {clip.description}
                                  </p>
                                )}
                                <div className="flex items-center justify-between text-xs text-muted-foreground">
                                  <span>{clip.duration.toFixed(1)}s</span>
                                  <Badge variant="secondary" className="text-xs">
                                    {Math.round(clip.viral_score * 100)}% viral
                                  </Badge>
                                </div>
                                <div className="flex flex-wrap gap-1 mt-1">
                                  {clip.sentiment && (
                                    <Badge variant="outline" className="text-xs px-1 py-0">
                                      {clip.sentiment}
                                    </Badge>
                                  )}
                                  {clip.emotion && (
                                    <Badge variant="outline" className="text-xs px-1 py-0">
                                      {clip.emotion}
                                    </Badge>
                                  )}
                                  {clip.has_subtitles && (
                                    <Badge variant="outline" className="text-xs px-1 py-0">
                                      Subtitles
                                    </Badge>
                                  )}
                                </div>
                              </div>
                              <div className="flex gap-2">
                                <Button size="sm" variant="outline" className="flex-1">
                                  <Play className="w-3 h-3 mr-1" />
                                  Preview
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  className="flex-1"
                                  onClick={() => downloadClip(video.id, clip.id, `clip_${clip.id}.mp4`)}
                                >
                                  <Download className="w-3 h-3 mr-1" />
                                  Download
                                </Button>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
