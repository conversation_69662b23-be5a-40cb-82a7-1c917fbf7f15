"""
Processing API routes for video analysis and clip generation
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Dict, Any
import logging

from ...core.database import get_db
from ...models.video import Video
from ...models.processing_job import ProcessingJob
from ...services.ai_processor import AIProcessor
from ...services.websocket_manager import WebSocketManager

logger = logging.getLogger(__name__)
router = APIRouter()

# WebSocket manager instance (should be shared with main app)
websocket_manager = WebSocketManager()

@router.post("/start/{video_id}")
async def start_processing(
    video_id: int,
    background_tasks: BackgroundTasks,
    client_id: str = None,
    db: AsyncSession = Depends(get_db)
):
    """Start the AI processing pipeline for a video"""
    
    # Get video from database
    video = await db.get(Video, video_id)
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")
    
    if video.status == "processing":
        raise HTTPException(status_code=400, detail="Video is already being processed")
    
    try:
        # Update video status
        video.status = "processing"
        await db.commit()
        
        # Create processing job
        job = ProcessingJob(
            video_id=video_id,
            job_type="full_pipeline",
            status="pending"
        )
        db.add(job)
        await db.commit()
        await db.refresh(job)
        
        # Start background processing
        ai_processor = AIProcessor(websocket_manager, client_id)
        background_tasks.add_task(ai_processor.process_video, video_id, job.id)
        
        logger.info(f"Started processing for video {video_id}, job {job.id}")
        
        return {
            "message": "Processing started",
            "video_id": video_id,
            "job_id": job.id,
            "status": "processing"
        }
        
    except Exception as e:
        # Revert video status on error
        video.status = "uploaded"
        await db.commit()
        
        logger.error(f"Error starting processing for video {video_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error starting processing: {str(e)}")

@router.get("/status/{video_id}")
async def get_processing_status(video_id: int, db: AsyncSession = Depends(get_db)):
    """Get the current processing status of a video"""
    
    video = await db.get(Video, video_id)
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")
    
    # Get latest processing job
    from sqlalchemy import select
    query = select(ProcessingJob).where(
        ProcessingJob.video_id == video_id
    ).order_by(ProcessingJob.created_at.desc()).limit(1)
    
    result = await db.execute(query)
    latest_job = result.scalar_one_or_none()
    
    response = {
        "video_id": video_id,
        "video_status": video.status,
        "has_transcript": video.transcript is not None,
        "clips_count": len(video.clips)
    }
    
    if latest_job:
        response.update({
            "job_id": latest_job.id,
            "job_status": latest_job.status,
            "progress": latest_job.progress,
            "current_step": latest_job.current_step,
            "started_at": latest_job.started_at,
            "estimated_completion": latest_job.estimated_completion,
            "error_message": latest_job.error_message
        })
    
    return response

@router.post("/cancel/{video_id}")
async def cancel_processing(video_id: int, db: AsyncSession = Depends(get_db)):
    """Cancel ongoing processing for a video"""
    
    video = await db.get(Video, video_id)
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")
    
    if video.status != "processing":
        raise HTTPException(status_code=400, detail="Video is not being processed")
    
    try:
        # Update video status
        video.status = "uploaded"
        
        # Cancel active processing jobs
        from sqlalchemy import select, update
        
        # Update running jobs to cancelled
        query = update(ProcessingJob).where(
            ProcessingJob.video_id == video_id,
            ProcessingJob.status.in_(["pending", "running"])
        ).values(status="cancelled")
        
        await db.execute(query)
        await db.commit()
        
        logger.info(f"Cancelled processing for video {video_id}")
        
        return {
            "message": "Processing cancelled",
            "video_id": video_id,
            "status": "cancelled"
        }
        
    except Exception as e:
        logger.error(f"Error cancelling processing for video {video_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error cancelling processing: {str(e)}")

@router.get("/jobs/{video_id}")
async def get_processing_jobs(video_id: int, db: AsyncSession = Depends(get_db)):
    """Get all processing jobs for a video"""
    
    video = await db.get(Video, video_id)
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")
    
    from sqlalchemy import select
    query = select(ProcessingJob).where(
        ProcessingJob.video_id == video_id
    ).order_by(ProcessingJob.created_at.desc())
    
    result = await db.execute(query)
    jobs = result.scalars().all()
    
    return {
        "video_id": video_id,
        "jobs": [
            {
                "id": job.id,
                "job_type": job.job_type,
                "status": job.status,
                "progress": job.progress,
                "current_step": job.current_step,
                "started_at": job.started_at,
                "completed_at": job.completed_at,
                "error_message": job.error_message,
                "created_at": job.created_at
            }
            for job in jobs
        ]
    }

@router.post("/retry/{video_id}")
async def retry_processing(
    video_id: int,
    background_tasks: BackgroundTasks,
    client_id: str = None,
    db: AsyncSession = Depends(get_db)
):
    """Retry processing for a failed video"""
    
    video = await db.get(Video, video_id)
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")
    
    if video.status == "processing":
        raise HTTPException(status_code=400, detail="Video is already being processed")
    
    try:
        # Reset video status
        video.status = "processing"
        await db.commit()
        
        # Create new processing job
        job = ProcessingJob(
            video_id=video_id,
            job_type="retry_pipeline",
            status="pending"
        )
        db.add(job)
        await db.commit()
        await db.refresh(job)
        
        # Start background processing
        ai_processor = AIProcessor(websocket_manager, client_id)
        background_tasks.add_task(ai_processor.process_video, video_id, job.id)
        
        logger.info(f"Retrying processing for video {video_id}, job {job.id}")
        
        return {
            "message": "Processing retry started",
            "video_id": video_id,
            "job_id": job.id,
            "status": "processing"
        }
        
    except Exception as e:
        video.status = "failed"
        await db.commit()
        
        logger.error(f"Error retrying processing for video {video_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error retrying processing: {str(e)}")
