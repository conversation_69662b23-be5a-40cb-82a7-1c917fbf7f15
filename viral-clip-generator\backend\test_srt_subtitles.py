#!/usr/bin/env python3

import asyncio
import os
import sys
import subprocess
from pathlib import Path

# Add the app directory to Python path
sys.path.append(str(Path(__file__).parent / 'app'))

from app.services.video_processor import VideoProcessor

async def test_srt_subtitles():
    """Test SRT subtitle embedding as separate streams"""
    
    print("📝 TESTING SRT SUBTITLE EMBEDDING")
    print("=" * 70)
    
    # Get a video file
    videos_dir = Path("uploads/videos")
    video_files = list(videos_dir.glob("*.mp4"))
    
    if not video_files:
        print("❌ No video files found")
        return
    
    test_video = str(video_files[0])
    print(f"✅ Using video: {test_video}")
    
    # Create test segments with proper timing
    test_segments = [
        {
            'start': 1.0,
            'end': 3.0,
            'text': 'Hello, this is a test subtitle',
            'words': [
                {'word': ' Hello,', 'start': 1.0, 'end': 1.5, 'confidence': 0.9},
                {'word': ' this', 'start': 1.5, 'end': 1.8, 'confidence': 0.95},
                {'word': ' is', 'start': 1.8, 'end': 2.0, 'confidence': 0.9},
                {'word': ' a', 'start': 2.0, 'end': 2.1, 'confidence': 0.95},
                {'word': ' test', 'start': 2.1, 'end': 2.5, 'confidence': 0.9},
                {'word': ' subtitle', 'start': 2.5, 'end': 3.0, 'confidence': 0.95}
            ]
        },
        {
            'start': 4.0,
            'end': 6.0,
            'text': 'This should appear and disappear',
            'words': [
                {'word': ' This', 'start': 4.0, 'end': 4.3, 'confidence': 0.9},
                {'word': ' should', 'start': 4.3, 'end': 4.7, 'confidence': 0.95},
                {'word': ' appear', 'start': 4.7, 'end': 5.2, 'confidence': 0.9},
                {'word': ' and', 'start': 5.2, 'end': 5.4, 'confidence': 0.95},
                {'word': ' disappear', 'start': 5.4, 'end': 6.0, 'confidence': 0.9}
            ]
        }
    ]
    
    processor = VideoProcessor()
    
    print(f"\n🎬 CREATING CLIP WITH SRT SUBTITLES")
    print("-" * 70)
    
    # Create test clip with SRT subtitles
    clip_start = 10.0
    clip_end = 20.0
    test_clip_path = "uploads/clips/TEST_SRT_SUBTITLES.mp4"
    
    print(f"📹 Creating clip: {clip_start}s - {clip_end}s")
    print(f"📝 With {len(test_segments)} subtitle segments")
    
    success = await processor.extract_clip(
        test_video,
        clip_start,
        clip_end,
        test_clip_path,
        crop_to_vertical=True,
        subtitle_segments=test_segments
    )
    
    if success and os.path.exists(test_clip_path):
        file_size = os.path.getsize(test_clip_path)
        print(f"✅ Clip created successfully: {file_size:,} bytes")
        
        # Test 1: Check for subtitle streams
        print(f"\n🔍 CHECKING FOR SUBTITLE STREAMS")
        print("-" * 50)
        
        try:
            cmd = [
                "ffprobe", 
                "-v", "quiet", 
                "-print_format", "json", 
                "-show_streams", 
                test_clip_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                import json
                data = json.loads(result.stdout)
                streams = data.get('streams', [])
                
                video_streams = [s for s in streams if s.get('codec_type') == 'video']
                audio_streams = [s for s in streams if s.get('codec_type') == 'audio']
                subtitle_streams = [s for s in streams if s.get('codec_type') == 'subtitle']
                
                print(f"📺 Video streams: {len(video_streams)}")
                print(f"🔊 Audio streams: {len(audio_streams)}")
                print(f"📝 Subtitle streams: {len(subtitle_streams)}")
                
                if subtitle_streams:
                    print(f"🎉 SUCCESS! SUBTITLES ARE EMBEDDED AS SEPARATE STREAMS!")
                    for i, sub_stream in enumerate(subtitle_streams):
                        codec = sub_stream.get('codec_name', 'unknown')
                        print(f"   Stream {i}: {codec}")
                        
                    print(f"\n✅ SUBTITLES WILL APPEAR/DISAPPEAR DURING PLAYBACK!")
                    print(f"✅ Users can enable/disable subtitles in video player!")
                else:
                    print(f"❌ NO SUBTITLE STREAMS FOUND")
            else:
                print(f"❌ FFprobe failed: {result.stderr}")
                
        except Exception as e:
            print(f"❌ Error checking streams: {e}")
        
        # Test 2: Try to extract subtitles to verify content
        print(f"\n📤 TESTING SUBTITLE EXTRACTION")
        print("-" * 50)
        
        try:
            subtitle_output = "extracted_srt_test.srt"
            
            cmd = [
                "ffmpeg", 
                "-i", test_clip_path,
                "-map", "0:s:0",  # Extract first subtitle stream
                "-c", "copy",
                "-y",  # Overwrite
                subtitle_output
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0 and os.path.exists(subtitle_output):
                subtitle_size = os.path.getsize(subtitle_output)
                print(f"✅ EXTRACTED SUBTITLES: {subtitle_size} bytes")
                
                # Read and show content
                with open(subtitle_output, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                print(f"📄 SRT Content:")
                print(content[:200] + "..." if len(content) > 200 else content)
                
                # Clean up
                os.remove(subtitle_output)
            else:
                print(f"❌ EXTRACTION FAILED")
                if result.stderr:
                    print(f"   Error: {result.stderr[:100]}...")
                
        except Exception as e:
            print(f"❌ Error extracting subtitles: {e}")
        
    else:
        print(f"❌ Clip creation failed")
    
    print(f"\n🎯 SUMMARY")
    print("=" * 70)
    print("📝 Testing SRT subtitle embedding as separate streams")
    print("✅ If subtitle streams are found, subtitles will appear/disappear!")
    print("🎬 Download the test clip and check if subtitles work in video player!")
    print("🎮 Users should be able to enable/disable subtitles manually!")

if __name__ == "__main__":
    asyncio.run(test_srt_subtitles())
