#!/usr/bin/env python3
"""
Test the EXACT subtitle flow used in viral clip generation
"""

import asyncio
import os
import sys
import logging
from pathlib import Path

# Add the app directory to Python path
sys.path.append('.')

from app.services.ai_processor import AIProcessor
from app.services.websocket_manager import WebSocketManager
from app.services.video_processor import VideoProcessor

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_real_subtitle_flow():
    """Test the EXACT subtitle flow used in viral clip generation"""
    
    print("🔍 TESTING REAL SUBTITLE FLOW")
    print("=" * 50)
    
    # Check if test video exists
    test_video_path = "uploads/videos/test-video.mp4"
    if not os.path.exists(test_video_path):
        print(f"❌ Test video not found: {test_video_path}")
        return
    
    print(f"✅ Test video found: {test_video_path}")
    
    # Initialize components EXACTLY like viral clip generation
    websocket_manager = WebSocketManager()
    ai_processor = AIProcessor(websocket_manager)
    
    # Create a mock video object
    class MockVideo:
        def __init__(self):
            self.id = 1
            self.file_path = test_video_path
            self.transcript = None
            self.transcript_with_timestamps = None
            self.status = "uploaded"
    
    video = MockVideo()
    
    print("\n🎤 STEP 1: GET REAL TRANSCRIPTION")
    print("-" * 40)
    
    # Extract audio and transcribe EXACTLY like the real pipeline
    audio_path = await ai_processor._extract_audio(video)
    
    class MockDB:
        async def commit(self):
            pass
    
    db = MockDB()
    transcript_data = await ai_processor._transcribe_audio(audio_path, video, db)
    
    print(f"✅ Transcription: {len(transcript_data['segments'])} segments")
    
    # Show first few segments to verify format
    print("\n📋 REAL TRANSCRIPT SEGMENTS:")
    for i, seg in enumerate(transcript_data['segments'][:3]):
        print(f"  {i+1}. [{seg['start']:.1f}s-{seg['end']:.1f}s]: {seg['text']}")
    
    print("\n🔥 STEP 2: DETECT VIRAL MOMENTS")
    print("-" * 40)
    
    # Detect viral moments EXACTLY like the real pipeline
    viral_segments = await ai_processor._detect_viral_moments(transcript_data)
    
    print(f"✅ Viral moments: {len(viral_segments)} found")
    
    if not viral_segments:
        print("❌ No viral segments found!")
        return
    
    # Take the first viral segment
    first_viral = viral_segments[0]
    print(f"\n📹 TESTING FIRST VIRAL SEGMENT:")
    print(f"   Time: {first_viral['start_time']:.1f}s - {first_viral['end_time']:.1f}s")
    print(f"   Text: {first_viral['text'][:80]}...")
    
    print("\n🎬 STEP 3: EXTRACT SUBTITLE SEGMENTS FOR THIS CLIP")
    print("-" * 50)
    
    # Extract subtitle segments EXACTLY like _generate_clips does
    all_transcript_segments = transcript_data.get("segments", [])
    clip_start = first_viral["start_time"]
    clip_end = first_viral["end_time"]
    
    clip_subtitle_segments = []
    for transcript_seg in all_transcript_segments:
        seg_start = transcript_seg.get('start', 0)
        seg_end = transcript_seg.get('end', 0)
        
        # Check if segment overlaps with clip timeframe
        if not (seg_end <= clip_start or seg_start >= clip_end):
            clip_subtitle_segments.append(transcript_seg)
    
    print(f"✅ Extracted {len(clip_subtitle_segments)} subtitle segments for clip")
    
    # Show the subtitle segments that will be used
    print("\n📝 SUBTITLE SEGMENTS FOR THIS CLIP:")
    for i, seg in enumerate(clip_subtitle_segments):
        print(f"  {i+1}. [{seg['start']:.1f}s-{seg['end']:.1f}s]: {seg['text']}")
    
    print("\n🎯 STEP 4: CREATE CLIP WITH REAL SUBTITLES")
    print("-" * 50)
    
    # Create clip EXACTLY like _generate_clips does
    processor = VideoProcessor()
    clip_path = "uploads/clips/REAL_FLOW_TEST.mp4"
    
    success = await processor.extract_clip(
        test_video_path,
        first_viral["start_time"],
        first_viral["end_time"],
        clip_path,
        crop_to_vertical=True,
        subtitle_segments=clip_subtitle_segments  # REAL SEGMENTS FROM REAL FLOW!
    )
    
    if success and os.path.exists(clip_path):
        file_size = os.path.getsize(clip_path)
        print(f"✅ REAL FLOW CLIP CREATED: {file_size} bytes")
        print(f"📁 File location: {clip_path}")
        
        # Verify the subtitle segments were processed
        print(f"\n🔍 SUBTITLE VERIFICATION:")
        print(f"   Clip timeframe: {clip_start:.1f}s - {clip_end:.1f}s")
        print(f"   Subtitle segments: {len(clip_subtitle_segments)}")
        print(f"   Expected subtitles:")
        for seg in clip_subtitle_segments:
            relative_start = max(0, seg['start'] - clip_start)
            relative_end = min(clip_end - clip_start, seg['end'] - clip_start)
            print(f"     [{relative_start:.1f}s-{relative_end:.1f}s]: {seg['text']}")
    else:
        print("❌ REAL FLOW CLIP FAILED!")
    
    # Clean up
    if os.path.exists(audio_path):
        os.remove(audio_path)
    
    print("\n🎉 REAL SUBTITLE FLOW TEST COMPLETE!")
    print("=" * 50)

if __name__ == "__main__":
    asyncio.run(test_real_subtitle_flow())
