"""
Processing Job model for tracking background tasks
"""

from sqlalchemy import <PERSON>umn, Integer, String, DateTime, Float, Text, Boolean, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from ..core.database import Base

class ProcessingJob(Base):
    __tablename__ = "processing_jobs"
    
    id = Column(Integer, primary_key=True, index=True)
    video_id = Column(Integer, ForeignKey("videos.id"), nullable=False)
    
    # Job information
    job_type = Column(String, nullable=False)  # transcription, clip_generation, subtitle_generation, etc.
    status = Column(String, default="pending")  # pending, running, completed, failed, cancelled
    
    # Progress tracking
    progress = Column(Float, default=0.0)  # 0-100 percentage
    current_step = Column(String, nullable=True)
    total_steps = Column(Integer, nullable=True)
    
    # Timing
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    estimated_completion = Column(DateTime(timezone=True), nullable=True)
    
    # Results and errors
    result_data = Column(Text, nullable=True)  # JSON format
    error_message = Column(Text, nullable=True)
    error_traceback = Column(Text, nullable=True)
    
    # Task queue information
    celery_task_id = Column(String, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    video = relationship("Video", back_populates="processing_jobs")
    
    def __repr__(self):
        return f"<ProcessingJob(id={self.id}, job_type='{self.job_type}', status='{self.status}', progress={self.progress}%)>"
