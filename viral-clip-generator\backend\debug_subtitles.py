#!/usr/bin/env python3
"""
Debug subtitle embedding issues
"""

import asyncio
import os
import sys
import logging
from pathlib import Path

# Add the app directory to Python path
sys.path.append('.')

from app.services.video_processor import VideoProcessor

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def debug_subtitle_embedding():
    """Debug why subtitles aren't appearing in videos"""
    
    print("🔍 DEBUGGING SUBTITLE EMBEDDING")
    print("=" * 50)
    
    # Check if test video exists
    test_video_path = "uploads/videos/test-video.mp4"
    if not os.path.exists(test_video_path):
        print(f"❌ Test video not found: {test_video_path}")
        return
    
    print(f"✅ Test video found: {test_video_path}")
    
    # Create simple test subtitle segments
    test_segments = [
        {"start": 0.0, "end": 5.0, "text": "This is a test subtitle"},
        {"start": 5.0, "end": 10.0, "text": "Second subtitle line"},
        {"start": 10.0, "end": 15.0, "text": "Third subtitle with animation"}
    ]
    
    processor = VideoProcessor()
    
    print("\n🎬 STEP 1: CREATING ASS SUBTITLE FILE")
    print("-" * 40)
    
    # Create subtitle file manually to inspect it
    output_dir = Path("uploads/clips")
    subtitle_file = await processor._create_subtitle_file(test_segments, 0.0, 15.0, output_dir)
    
    if subtitle_file and os.path.exists(subtitle_file):
        print(f"✅ ASS file created: {subtitle_file}")
        
        # Read and display the ASS content
        with open(subtitle_file, 'r', encoding='utf-8') as f:
            ass_content = f.read()
        
        print("\n📝 ASS FILE CONTENT:")
        print("-" * 30)
        print(ass_content[:500] + "..." if len(ass_content) > 500 else ass_content)
        
        print("\n🔧 STEP 2: TESTING FFMPEG COMMAND")
        print("-" * 40)
        
        # Test FFmpeg command manually
        clip_path = "uploads/clips/DEBUG_SUBTITLE_TEST.mp4"
        
        # Build the exact FFmpeg command that would be used
        escaped_subtitle_path = subtitle_file.replace('\\', '\\\\').replace(':', '\\:').replace("'", "\\'")
        
        cmd = [
            'ffmpeg',
            '-i', test_video_path,
            '-ss', '0',
            '-t', '15',
            '-c:v', 'libx264',
            '-c:a', 'aac',
            '-preset', 'fast',
            '-crf', '23',
            '-vf', f"crop=ih*9/16:ih,ass='{escaped_subtitle_path}'",
            '-y', clip_path
        ]
        
        print("FFmpeg command:")
        print(" ".join(cmd))
        
        # Execute the command
        import subprocess
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=60
        )
        
        print(f"\n📊 FFMPEG RESULT:")
        print(f"Return code: {result.returncode}")
        print(f"STDOUT: {result.stdout}")
        print(f"STDERR: {result.stderr}")
        
        if result.returncode == 0 and os.path.exists(clip_path):
            file_size = os.path.getsize(clip_path)
            print(f"✅ Video created: {file_size} bytes")
            
            # Test if subtitles are actually embedded
            print("\n🔍 STEP 3: CHECKING IF SUBTITLES ARE EMBEDDED")
            print("-" * 50)
            
            # Use ffprobe to check for subtitle streams
            probe_cmd = ['ffprobe', '-v', 'quiet', '-print_format', 'json', '-show_streams', clip_path]
            probe_result = subprocess.run(probe_cmd, capture_output=True, text=True)
            
            if probe_result.returncode == 0:
                import json
                streams = json.loads(probe_result.stdout)
                
                subtitle_streams = [s for s in streams.get('streams', []) if s.get('codec_type') == 'subtitle']
                print(f"Subtitle streams found: {len(subtitle_streams)}")
                
                if subtitle_streams:
                    for i, stream in enumerate(subtitle_streams):
                        print(f"  Stream {i}: {stream.get('codec_name', 'unknown')}")
                else:
                    print("❌ NO SUBTITLE STREAMS FOUND!")
                    print("This means subtitles are burned into the video, not embedded as streams")
            
        else:
            print(f"❌ FFmpeg failed!")
            print(f"Error: {result.stderr}")
        
        # Don't delete the subtitle file for inspection
        print(f"\n📁 ASS file kept for inspection: {subtitle_file}")
        
    else:
        print("❌ Failed to create ASS subtitle file")

if __name__ == "__main__":
    asyncio.run(debug_subtitle_embedding())
