#!/usr/bin/env python3
"""
Test the complete AI processing pipeline with advanced models
"""

import asyncio
import sys
import logging
from app.services.ai_processor import AIProcessor
from app.services.websocket_manager import WebSocketManager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_ai_pipeline():
    """Test the complete AI processing pipeline"""
    
    print("🧠 Testing Advanced AI Processing Pipeline")
    print("=" * 60)
    
    try:
        # Initialize components
        websocket_manager = WebSocketManager()
        ai_processor = AIProcessor(websocket_manager, client_id="test-client")
        
        print("🔄 Loading AI models...")
        await ai_processor.load_models()
        print("✅ AI models loaded successfully!")
        
        # Test 1: Text Analysis
        print("\n📝 Testing text analysis...")
        test_text = "This is absolutely mind-blowing! You won't believe what happens next. This secret technique will change everything!"
        
        # Test sentiment analysis
        sentiment_result = await ai_processor._analyze_sentiment_advanced(test_text)
        print(f"🎭 Sentiment: {sentiment_result}")
        
        # Test emotion analysis
        emotion_result = await ai_processor._analyze_emotion_advanced(test_text)
        print(f"😊 Emotion: {emotion_result}")
        
        # Test LM Studio analysis
        if ai_processor.lm_studio_client:
            lm_result = await ai_processor._analyze_with_lm_studio(test_text)
            print(f"🤖 LM Studio Analysis: {lm_result}")
        
        # Test text features
        features_score = await ai_processor._analyze_text_features(test_text)
        print(f"📊 Text Features Score: {features_score}")
        
        # Test keyword extraction
        keywords = await ai_processor._extract_keywords(test_text)
        print(f"🔑 Keywords: {keywords}")
        
        # Test engagement factors
        engagement = await ai_processor._analyze_engagement_factors(test_text)
        print(f"📈 Engagement Factors: {engagement}")
        
        # Test 2: Mock Viral Moment Detection
        print("\n🎯 Testing viral moment detection...")
        mock_transcript = {
            "segments": [
                {
                    "start": 0.0,
                    "end": 15.0,
                    "text": "This is absolutely incredible! You won't believe what happens next!"
                },
                {
                    "start": 15.0,
                    "end": 30.0,
                    "text": "The secret technique that changed everything. This will blow your mind!"
                },
                {
                    "start": 30.0,
                    "end": 45.0,
                    "text": "Wait for it... the most amazing transformation you've ever seen!"
                }
            ]
        }
        
        viral_moments = await ai_processor._detect_viral_moments(mock_transcript)
        print(f"🔥 Detected {len(viral_moments)} viral moments:")
        
        for i, moment in enumerate(viral_moments):
            print(f"\n  📹 Moment {i+1}:")
            print(f"    ⏱️  Time: {moment['start_time']:.1f}s - {moment['end_time']:.1f}s")
            print(f"    📝 Text: {moment['text'][:60]}...")
            print(f"    🎯 Viral Score: {moment['viral_score']:.2f}")
            print(f"    🎭 Sentiment: {moment['sentiment']} ({moment['sentiment_score']:.2f})")
            print(f"    😊 Emotion: {moment['emotion']} ({moment['emotion_score']:.2f})")
            if 'lm_analysis' in moment and moment['lm_analysis']:
                print(f"    🤖 LM Analysis: {moment['lm_analysis'].get('viral_potential', 'N/A')}")
        
        print("\n" + "=" * 60)
        print("🎉 AI PIPELINE TEST COMPLETED SUCCESSFULLY!")
        print("🚀 All advanced AI models are working correctly!")
        print("🔥 Ready for viral clip generation!")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"❌ Pipeline test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_model_availability():
    """Test which AI models are available"""
    
    print("\n🔍 Checking AI Model Availability:")
    print("-" * 40)
    
    try:
        # Test imports
        try:
            import whisper
            print("✅ Whisper: Available")
        except ImportError:
            print("❌ Whisper: Not available")
        
        try:
            from transformers import pipeline
            print("✅ Transformers: Available")
        except ImportError:
            print("❌ Transformers: Not available")
        
        try:
            from openai import AsyncOpenAI
            print("✅ OpenAI (LM Studio): Available")
        except ImportError:
            print("❌ OpenAI (LM Studio): Not available")
        
        try:
            from vaderSentiment.vaderSentiment import SentimentIntensityAnalyzer
            print("✅ VADER Sentiment: Available")
        except ImportError:
            print("❌ VADER Sentiment: Not available")
        
        try:
            import cv2
            print("✅ OpenCV: Available")
        except ImportError:
            print("❌ OpenCV: Not available")
        
        try:
            from ultralytics import YOLO
            print("✅ YOLO v8: Available")
        except ImportError:
            print("❌ YOLO v8: Not available")
        
        try:
            import spacy
            print("✅ spaCy: Available")
        except ImportError:
            print("⚠️ spaCy: Not available (optional)")
        
        try:
            from detoxify import Detoxify
            print("✅ Detoxify: Available")
        except ImportError:
            print("⚠️ Detoxify: Not available (optional)")
            
    except Exception as e:
        print(f"❌ Model check failed: {e}")

if __name__ == "__main__":
    async def main():
        await test_model_availability()
        result = await test_ai_pipeline()
        return result
    
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
