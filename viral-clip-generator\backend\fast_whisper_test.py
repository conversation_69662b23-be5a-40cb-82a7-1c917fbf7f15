#!/usr/bin/env python3
"""
Fast Whisper test - downloads smaller model quickly
"""

import asyncio
import sys
import logging
import time

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def fast_whisper_test():
    """Test Whisper with smaller, faster model"""
    
    print("🚀 FAST WHISPER TEST - Using Base Model")
    print("=" * 50)
    
    try:
        print("📦 Importing Whisper...")
        import whisper
        
        print("⬇️ Loading Whisper base model (much faster)...")
        start_time = time.time()
        
        # Use base model - only ~140MB vs 2.9GB for large-v3
        model = whisper.load_model("base")
        
        load_time = time.time() - start_time
        print(f"✅ Whisper base model loaded in {load_time:.1f} seconds!")
        
        # Test transcription with a simple audio file (if available)
        print("\n🎤 Testing transcription...")
        
        # Create a simple test
        print("📝 Model info:")
        print(f"   - Model: {model.dims}")
        print(f"   - Device: {next(model.parameters()).device}")
        
        print("\n🎉 WHISPER TEST COMPLETED!")
        print("🔥 Ready for fast audio processing!")
        print("=" * 50)
        
        return True
        
    except Exception as e:
        print(f"❌ Whisper test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_all_models():
    """Test availability of all AI models with fast options"""
    
    print("\n🔍 Testing Fast AI Model Setup:")
    print("-" * 40)
    
    try:
        # Test Whisper (fast)
        print("🎤 Testing Whisper (base model)...")
        import whisper
        model = whisper.load_model("base")
        print("✅ Whisper base: Ready!")
        
        # Test Transformers
        print("🤖 Testing Transformers...")
        from transformers import pipeline
        print("✅ Transformers: Ready!")
        
        # Test LM Studio connection
        print("🧠 Testing LM Studio connection...")
        from openai import AsyncOpenAI
        client = AsyncOpenAI(
            base_url="http://localhost:1234/v1",
            api_key="lm-studio"
        )
        
        # Quick test
        try:
            response = await client.chat.completions.create(
                model="llama-3.2-1b-instruct",
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=10
            )
            print("✅ LM Studio: Connected and working!")
        except Exception as e:
            print(f"⚠️ LM Studio: {e}")
        
        # Test other models
        print("📊 Testing VADER Sentiment...")
        from vaderSentiment.vaderSentiment import SentimentIntensityAnalyzer
        analyzer = SentimentIntensityAnalyzer()
        test_score = analyzer.polarity_scores("This is amazing!")
        print(f"✅ VADER: Working! (test score: {test_score['compound']:.2f})")
        
        print("🎯 Testing YOLO...")
        from ultralytics import YOLO
        print("✅ YOLO: Ready!")
        
        print("👁️ Testing OpenCV...")
        import cv2
        print(f"✅ OpenCV: Ready! (version {cv2.__version__})")
        
        print("\n🎉 ALL FAST MODELS READY!")
        print("🚀 System optimized for speed!")
        
    except Exception as e:
        print(f"❌ Model test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    async def main():
        await fast_whisper_test()
        await test_all_models()
        return True
    
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
