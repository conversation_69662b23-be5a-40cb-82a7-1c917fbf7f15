import asyncio
import os
import sys
import json
import sqlite3
from pathlib import Path

# Add the app directory to the Python path
sys.path.append(str(Path(__file__).parent / "app"))

from app.services.ai_processor import AIProcessor
from app.services.video_processor import VideoProcessor

async def debug_real_pipeline():
    """Debug the actual pipeline to see what's happening with real transcript data"""
    
    print("🔍 DEBUGGING REAL PIPELINE SUBTITLE FLOW")
    print("=" * 60)
    
    # Check if test video exists
    test_video_path = "uploads/videos/test-video.mp4"
    if not os.path.exists(test_video_path):
        print("❌ Test video not found!")
        return
    
    print(f"✅ Test video found: {test_video_path}")
    
    # Initialize components
    class MockWebSocketManager:
        async def send_processing_update(self, client_id, job_id, status, progress, message):
            print(f"📡 {progress:.0f}% - {message}")
        async def send_processing_complete(self, client_id, video_id, clip_count):
            print(f"📡 Complete: {clip_count} clips")
        async def send_clip_generated(self, client_id, clip_data):
            print(f"📡 Clip: {clip_data.get('title', 'Untitled')}")
    
    class MockVideo:
        def __init__(self):
            self.id = 9999
            self.file_path = test_video_path
            self.transcript = None
            self.transcript_with_timestamps = None
    
    class MockDB:
        async def commit(self):
            pass
    
    ai_processor = AIProcessor(MockWebSocketManager())
    video = MockVideo()
    db = MockDB()
    
    print("\n🎤 STEP 1: REAL TRANSCRIPTION")
    print("-" * 40)
    
    # Get real transcription
    audio_path = await ai_processor._extract_audio(video)
    transcript_data = await ai_processor._transcribe_audio(audio_path, video, db)
    
    print(f"✅ Transcription: {len(transcript_data['segments'])} segments")
    
    # Check if segments have words
    segments_with_words = [s for s in transcript_data['segments'] if 'words' in s and s['words']]
    print(f"✅ Segments with words: {len(segments_with_words)}/{len(transcript_data['segments'])}")
    
    if len(segments_with_words) == 0:
        print("❌ NO SEGMENTS HAVE WORDS - THIS IS THE PROBLEM!")
        return
    
    # Show sample segment
    first_seg = transcript_data['segments'][0]
    print(f"\n📋 FIRST SEGMENT:")
    print(f"   Text: '{first_seg['text']}'")
    print(f"   Start: {first_seg['start']:.1f}s - End: {first_seg['end']:.1f}s")
    print(f"   Has words: {'words' in first_seg}")
    if 'words' in first_seg:
        print(f"   Word count: {len(first_seg['words'])}")
        if first_seg['words']:
            print(f"   First word: '{first_seg['words'][0]['word']}' ({first_seg['words'][0]['start']:.1f}s)")
    
    print("\n🔥 STEP 2: VIRAL MOMENT DETECTION")
    print("-" * 40)
    
    # Detect viral moments
    viral_segments = await ai_processor._detect_viral_moments(transcript_data)
    print(f"✅ Viral moments: {len(viral_segments)}")
    
    if not viral_segments:
        print("❌ No viral moments detected!")
        return
    
    # Show first viral moment
    first_viral = viral_segments[0]
    print(f"\n📋 FIRST VIRAL MOMENT:")
    print(f"   Time: {first_viral['start_time']:.1f}s - {first_viral['end_time']:.1f}s")
    print(f"   Text: '{first_viral['text'][:100]}...'")
    print(f"   Score: {first_viral['viral_score']:.2f}")
    
    print("\n🎬 STEP 3: CLIP GENERATION (REAL FLOW)")
    print("-" * 40)
    
    # Simulate the exact _generate_clips process
    all_transcript_segments = transcript_data.get("segments", [])
    
    # Extract subtitle segments for first viral moment (EXACT same logic)
    clip_start = first_viral["start_time"]
    clip_end = first_viral["end_time"]
    
    clip_subtitle_segments = []
    for transcript_seg in all_transcript_segments:
        seg_start = transcript_seg.get('start', 0)
        seg_end = transcript_seg.get('end', 0)
        
        # Check if segment overlaps with clip timeframe
        if not (seg_end <= clip_start or seg_start >= clip_end):
            clip_subtitle_segments.append(transcript_seg)
    
    print(f"📝 Clip timeframe: {clip_start:.1f}s - {clip_end:.1f}s")
    print(f"📝 Subtitle segments: {len(clip_subtitle_segments)}")
    
    # Check if these segments have words
    subtitle_segments_with_words = [s for s in clip_subtitle_segments if 'words' in s and s['words']]
    print(f"📝 Subtitle segments with words: {len(subtitle_segments_with_words)}/{len(clip_subtitle_segments)}")
    
    if len(subtitle_segments_with_words) == 0:
        print("❌ SUBTITLE SEGMENTS DON'T HAVE WORDS!")
        print("🔍 Checking what's in the segments:")
        for i, seg in enumerate(clip_subtitle_segments[:3]):
            print(f"   Segment {i+1}: {list(seg.keys())}")
            print(f"      Text: '{seg['text'][:50]}...'")
            print(f"      Has words: {'words' in seg}")
        return
    
    # Show sample subtitle segments
    print(f"\n📋 SUBTITLE SEGMENTS FOR CLIP:")
    for i, seg in enumerate(clip_subtitle_segments[:3]):
        words_count = len(seg.get('words', []))
        print(f"   {i+1}: {seg['start']:.1f}s-{seg['end']:.1f}s '{seg['text'][:40]}...' ({words_count} words)")
    
    print("\n🎬 STEP 4: ACTUAL CLIP CREATION")
    print("-" * 40)
    
    # Create clip using the EXACT same method
    processor = VideoProcessor()
    clip_path = "uploads/clips/REAL_PIPELINE_DEBUG.mp4"
    
    success = await processor.extract_clip(
        video.file_path,
        first_viral["start_time"],
        first_viral["end_time"],
        clip_path,
        crop_to_vertical=True,
        subtitle_segments=clip_subtitle_segments  # REAL SEGMENTS WITH WORDS!
    )
    
    if success and os.path.exists(clip_path):
        file_size = os.path.getsize(clip_path)
        print(f"✅ Clip created: {file_size:,} bytes")
        print(f"📁 Location: {clip_path}")
        
        # Test subtitle file creation directly
        print("\n🔧 STEP 5: DIRECT SUBTITLE FILE TEST")
        print("-" * 40)
        
        output_dir = Path("uploads/clips")
        subtitle_file = await processor._create_subtitle_file(
            clip_subtitle_segments, clip_start, clip_end, output_dir
        )
        
        if subtitle_file and os.path.exists(subtitle_file):
            with open(subtitle_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            dialogue_lines = [line for line in content.splitlines() if line.startswith('Dialogue:')]
            print(f"✅ Subtitle file: {len(content)} chars, {len(dialogue_lines)} dialogues")
            
            if dialogue_lines:
                print("✅ SUBTITLES WILL BE VISIBLE!")
                print(f"📝 Sample dialogue: {dialogue_lines[0][:80]}...")
            else:
                print("❌ NO DIALOGUE EVENTS - SUBTITLES WON'T BE VISIBLE!")
            
            # Clean up
            os.remove(subtitle_file)
        else:
            print("❌ Failed to create subtitle file")
    else:
        print("❌ Failed to create clip")
    
    # Clean up
    if os.path.exists(audio_path):
        os.remove(audio_path)
    
    print("\n🎉 REAL PIPELINE DEBUG COMPLETE!")
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(debug_real_pipeline())
