#!/usr/bin/env python3

import asyncio
import os
import sys
from pathlib import Path

# Add the app directory to Python path
sys.path.append(str(Path(__file__).parent / 'app'))

from app.services.video_processor import VideoProcessor

async def debug_srt_generation():
    """Debug SRT file generation"""
    
    print("🔍 DEBUGGING SRT FILE GENERATION")
    print("=" * 70)
    
    # Create test segments
    test_segments = [
        {
            'start': 1.0,
            'end': 3.0,
            'text': 'Hello, this is a test subtitle',
            'words': []
        },
        {
            'start': 4.0,
            'end': 6.0,
            'text': 'This should appear and disappear',
            'words': []
        }
    ]
    
    processor = VideoProcessor()
    
    print(f"\n📝 CREATING SRT FILE")
    print("-" * 50)
    
    # Create SRT file manually to inspect it
    output_dir = Path("uploads/clips")
    subtitle_file = await processor._create_subtitle_file(test_segments, 0.0, 10.0, output_dir)
    
    if subtitle_file and os.path.exists(subtitle_file):
        print(f"✅ SRT file created: {subtitle_file}")
        
        # Read and display the SRT content
        with open(subtitle_file, 'r', encoding='utf-8') as f:
            srt_content = f.read()
        
        print(f"\n📄 SRT FILE CONTENT:")
        print("-" * 30)
        print(srt_content)
        print("-" * 30)
        
        # Check file size
        file_size = os.path.getsize(subtitle_file)
        print(f"📊 File size: {file_size} bytes")
        
        # Validate SRT format
        lines = srt_content.strip().split('\n')
        print(f"📝 Total lines: {len(lines)}")
        
        # Check if it follows SRT format
        valid_srt = True
        if len(lines) >= 4:
            try:
                # First line should be a number
                int(lines[0])
                # Second line should have time format
                if '-->' not in lines[1]:
                    valid_srt = False
                # Third line should be text
                if not lines[2].strip():
                    valid_srt = False
            except:
                valid_srt = False
        else:
            valid_srt = False
        
        if valid_srt:
            print(f"✅ SRT format appears valid")
        else:
            print(f"❌ SRT format is invalid")
        
        # Don't clean up - keep for inspection
        print(f"🔍 SRT file kept for inspection: {subtitle_file}")
        
    else:
        print(f"❌ SRT file creation failed")
    
    print(f"\n🎯 NEXT STEPS")
    print("=" * 70)
    print("1. Check the SRT content above")
    print("2. Verify the time format is correct")
    print("3. Ensure proper line breaks and structure")

if __name__ == "__main__":
    asyncio.run(debug_srt_generation())
