#!/usr/bin/env python3
"""
Test script to verify LM Studio connection and llama-3.2-1b-instruct model
"""

import asyncio
import sys
from openai import AsyncOpenAI

async def test_lm_studio():
    """Test LM Studio connection and model"""
    
    print("🔍 Testing LM Studio connection...")
    print("📍 URL: http://localhost:1234")
    print("🤖 Model: llama-3.2-1b-instruct")
    print("-" * 50)
    
    try:
        # Initialize LM Studio client
        client = AsyncOpenAI(
            base_url="http://localhost:1234/v1",
            api_key="lm-studio"  # LM Studio doesn't require real API key
        )
        
        # Test 1: List available models
        print("📋 Testing /v1/models endpoint...")
        try:
            models = await client.models.list()
            print("✅ Models endpoint working!")
            print(f"📦 Available models: {[model.id for model in models.data]}")
        except Exception as e:
            print(f"❌ Models endpoint failed: {e}")
            return False
        
        # Test 2: Simple chat completion
        print("\n💬 Testing /v1/chat/completions endpoint...")
        try:
            response = await client.chat.completions.create(
                model="llama-3.2-1b-instruct",
                messages=[
                    {"role": "user", "content": "Hello! Can you respond with just 'LM Studio is working!'?"}
                ],
                temperature=0.3,
                max_tokens=50
            )
            
            if response.choices and response.choices[0].message:
                content = response.choices[0].message.content
                print("✅ Chat completions working!")
                print(f"🤖 Response: {content}")
            else:
                print("❌ No response content received")
                return False
                
        except Exception as e:
            print(f"❌ Chat completions failed: {e}")
            return False
        
        # Test 3: Viral analysis prompt (like our AI processor uses)
        print("\n🎯 Testing viral analysis prompt...")
        try:
            viral_prompt = """
            Analyze this text for viral potential on social media:
            "This is absolutely incredible! You won't believe what happened next!"
            
            Rate the viral potential from 0.0 to 1.0 and explain why.
            Respond in JSON format: {"viral_potential": 0.8, "reasoning": "explanation"}
            """
            
            response = await client.chat.completions.create(
                model="llama-3.2-1b-instruct",
                messages=[{"role": "user", "content": viral_prompt}],
                temperature=0.3,
                max_tokens=200
            )
            
            if response.choices and response.choices[0].message:
                content = response.choices[0].message.content
                print("✅ Viral analysis working!")
                print(f"🎯 Analysis: {content}")
            else:
                print("❌ No viral analysis response")
                return False
                
        except Exception as e:
            print(f"❌ Viral analysis failed: {e}")
            return False
        
        print("\n" + "=" * 50)
        print("🎉 ALL TESTS PASSED!")
        print("🚀 LM Studio integration is ready for viral clip generation!")
        print("=" * 50)
        return True
        
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        print("\n🔧 Troubleshooting:")
        print("1. Make sure LM Studio is running")
        print("2. Check that the server is on localhost:1234")
        print("3. Verify llama-3.2-1b-instruct model is loaded")
        print("4. Ensure the model is started in LM Studio")
        return False

if __name__ == "__main__":
    result = asyncio.run(test_lm_studio())
    sys.exit(0 if result else 1)
