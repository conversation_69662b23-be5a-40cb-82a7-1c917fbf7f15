import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// API Configuration
export const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'

// API Client
export class APIClient {
  private baseURL: string

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL
  }

  async uploadVideo(file: File, title?: string, description?: string) {
    const formData = new FormData()
    formData.append('file', file)
    if (title) formData.append('title', title)
    if (description) formData.append('description', description)

    const response = await fetch(`${this.baseURL}/api/upload/video`, {
      method: 'POST',
      body: formData,
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.detail || 'Upload failed')
    }

    return response.json()
  }

  async startProcessing(videoId: number, clientId?: string) {
    const url = new URL(`${this.baseURL}/api/processing/start/${videoId}`)
    if (clientId) url.searchParams.append('client_id', clientId)

    const response = await fetch(url.toString(), {
      method: 'POST',
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.detail || 'Processing failed to start')
    }

    return response.json()
  }

  async getProcessingStatus(videoId: number) {
    const response = await fetch(`${this.baseURL}/api/processing/status/${videoId}`)

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.detail || 'Failed to get status')
    }

    return response.json()
  }

  async getVideoClips(videoId: number) {
    const response = await fetch(`${this.baseURL}/api/clips/video/${videoId}`)

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.detail || 'Failed to get clips')
    }

    return response.json()
  }

  async getVideoInfo(videoId: number) {
    const response = await fetch(`${this.baseURL}/api/upload/video/${videoId}`)

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.detail || 'Failed to get video info')
    }

    return response.json()
  }

  async getAllVideos() {
    const response = await fetch(`${this.baseURL}/api/upload/videos`)

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.detail || 'Failed to get videos')
    }

    return response.json()
  }

  async getAllClips() {
    const response = await fetch(`${this.baseURL}/api/clips/`)

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.detail || 'Failed to get clips')
    }

    return response.json()
  }
}

// WebSocket Manager
export class WebSocketManager {
  private ws: WebSocket | null = null
  private clientId: string
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000

  constructor(clientId?: string) {
    this.clientId = clientId || Math.random().toString(36).substr(2, 9)
  }

  connect(onMessage: (data: any) => void, onError?: (error: Event) => void) {
    const wsUrl = `ws://localhost:8000/ws/${this.clientId}`

    try {
      this.ws = new WebSocket(wsUrl)

      this.ws.onopen = () => {
        console.log('WebSocket connected')
        this.reconnectAttempts = 0
      }

      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          onMessage(data)
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error)
        }
      }

      this.ws.onclose = () => {
        console.log('WebSocket disconnected')
        this.attemptReconnect(onMessage, onError)
      }

      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error)
        if (onError) onError(error)
      }
    } catch (error) {
      console.error('Failed to create WebSocket:', error)
      if (onError) onError(error as Event)
    }
  }

  private attemptReconnect(onMessage: (data: any) => void, onError?: (error: Event) => void) {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)

      setTimeout(() => {
        this.connect(onMessage, onError)
      }, this.reconnectDelay * this.reconnectAttempts)
    }
  }

  send(data: any) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(data))
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
  }

  getClientId() {
    return this.clientId
  }
}

// Create singleton instances
export const apiClient = new APIClient()
export const wsManager = new WebSocketManager()
