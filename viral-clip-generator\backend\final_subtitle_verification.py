import asyncio
import os
import sys
from pathlib import Path

# Add the app directory to the Python path
sys.path.append(str(Path(__file__).parent / "app"))

from app.services.video_processor import VideoProcessor

async def final_subtitle_verification():
    """Final verification that subtitles are working with real word timing"""
    
    print("🔍 FINAL SUBTITLE VERIFICATION")
    print("=" * 50)
    
    # Create test segments with word timing (like what comes from fixed Whisper)
    test_segments = [
        {
            'start': 0.0,
            'end': 3.0,
            'text': 'Hello world this is a test',
            'words': [
                {'word': ' Hello', 'start': 0.0, 'end': 0.5, 'confidence': 0.9},
                {'word': ' world', 'start': 0.5, 'end': 1.0, 'confidence': 0.95},
                {'word': ' this', 'start': 1.0, 'end': 1.3, 'confidence': 0.9},
                {'word': ' is', 'start': 1.3, 'end': 1.5, 'confidence': 0.95},
                {'word': ' a', 'start': 1.5, 'end': 1.6, 'confidence': 0.9},
                {'word': ' test', 'start': 1.6, 'end': 3.0, 'confidence': 0.9}
            ]
        },
        {
            'start': 3.0,
            'end': 6.0,
            'text': 'Subtitles should be visible now',
            'words': [
                {'word': ' Subtitles', 'start': 3.0, 'end': 3.8, 'confidence': 0.9},
                {'word': ' should', 'start': 3.8, 'end': 4.2, 'confidence': 0.95},
                {'word': ' be', 'start': 4.2, 'end': 4.4, 'confidence': 0.9},
                {'word': ' visible', 'start': 4.4, 'end': 5.0, 'confidence': 0.95},
                {'word': ' now', 'start': 5.0, 'end': 6.0, 'confidence': 0.9}
            ]
        }
    ]
    
    processor = VideoProcessor()
    
    print("\n📝 STEP 1: CREATE SUBTITLE FILE WITH WORD TIMING")
    print("-" * 50)
    
    output_dir = Path("uploads/clips")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Test subtitle file creation
    subtitle_file = await processor._create_subtitle_file(test_segments, 0.0, 6.0, output_dir)
    
    if subtitle_file and os.path.exists(subtitle_file):
        print(f"✅ Subtitle file created: {subtitle_file}")
        
        # Read and analyze content
        with open(subtitle_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"📄 File size: {len(content)} characters")
        
        # Check for dialogue events
        dialogue_lines = [line for line in content.splitlines() if line.startswith('Dialogue:')]
        print(f"📄 Dialogue events: {len(dialogue_lines)}")
        
        if dialogue_lines:
            print("\n📝 SUBTITLE CONTENT:")
            print("-" * 30)
            print(content)
            print("-" * 30)
            
            print("\n✅ SUBTITLE FILE ANALYSIS:")
            print(f"   ✅ Has Script Info: {'[Script Info]' in content}")
            print(f"   ✅ Has Styles: {'[V4+ Styles]' in content}")
            print(f"   ✅ Has Events: {'[Events]' in content}")
            print(f"   ✅ Has Dialogues: {len(dialogue_lines) > 0}")
            print(f"   ✅ Has Karaoke: {'\\k' in content}")
            print(f"   ✅ Has Animations: {'\\t(' in content}")
            
            if all([
                '[Script Info]' in content,
                '[V4+ Styles]' in content,
                '[Events]' in content,
                len(dialogue_lines) > 0,
                '\\k' in content
            ]):
                print("\n🎉 SUBTITLE FILE IS COMPLETE AND VALID!")
                print("✅ Word-by-word timing is working")
                print("✅ Karaoke effects are included")
                print("✅ All required ASS sections present")
                
                # Test with actual video
                print("\n🎬 STEP 2: TEST WITH ACTUAL VIDEO")
                print("-" * 50)
                
                test_video_path = "uploads/videos/test-video.mp4"
                if os.path.exists(test_video_path):
                    clip_path = "uploads/clips/FINAL_SUBTITLE_VERIFICATION.mp4"
                    
                    success = await processor.extract_clip(
                        test_video_path,
                        0.0,
                        6.0,
                        clip_path,
                        crop_to_vertical=True,
                        subtitle_segments=test_segments
                    )
                    
                    if success and os.path.exists(clip_path):
                        file_size = os.path.getsize(clip_path)
                        print(f"✅ Final test clip created: {file_size:,} bytes")
                        print(f"📁 Location: {clip_path}")
                        print("\n🎉 SUBTITLE FIX IS WORKING!")
                        print("🎯 All future viral clips will have visible subtitles!")
                    else:
                        print("❌ Failed to create final test clip")
                else:
                    print("⚠️  Test video not found, but subtitle generation is working")
            else:
                print("\n❌ SUBTITLE FILE IS INCOMPLETE!")
        else:
            print("\n❌ NO DIALOGUE EVENTS FOUND!")
        
        # Clean up
        if os.path.exists(subtitle_file):
            os.remove(subtitle_file)
    else:
        print("❌ Failed to create subtitle file")
    
    print("\n🎉 FINAL VERIFICATION COMPLETE!")
    print("=" * 50)

if __name__ == "__main__":
    asyncio.run(final_subtitle_verification())
