#!/usr/bin/env python3

import asyncio
import os
import sys
import json
from pathlib import Path

# Add the app directory to Python path
sys.path.append(str(Path(__file__).parent / 'app'))

from app.services.video_processor import VideoProcessor

async def final_subtitle_test():
    """Final comprehensive test of the subtitle timing fix"""
    
    print("🎬 FINAL SUBTITLE TIMING TEST")
    print("=" * 70)
    
    # Find available video
    videos_dir = Path("uploads/videos")
    video_files = list(videos_dir.glob("*.mp4"))
    
    if not video_files:
        print("❌ No video files found")
        return
    
    test_video = str(video_files[0])
    print(f"✅ Using video: {test_video}")
    
    # Get real transcript data
    import sqlite3
    conn = sqlite3.connect('viral_clips.db')
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT transcript_with_timestamps 
        FROM videos 
        WHERE transcript_with_timestamps IS NOT NULL 
        ORDER BY id DESC 
        LIMIT 1
    ''')
    
    result = cursor.fetchone()
    if not result:
        print("❌ No transcript data found")
        return
    
    transcript_data = json.loads(result[0])
    segments = transcript_data.get('segments', [])
    
    print(f"✅ Found {len(segments)} transcript segments")
    
    # Test 3 different clips with different timeframes
    test_clips = [
        {"name": "CLIP_A", "start": 20.0, "end": 35.0},
        {"name": "CLIP_B", "start": 80.0, "end": 95.0}, 
        {"name": "CLIP_C", "start": 150.0, "end": 165.0}
    ]
    
    processor = VideoProcessor()
    
    print(f"\n🎯 TESTING 3 CLIPS WITH DIFFERENT TIMEFRAMES")
    print("-" * 70)
    
    for i, clip_data in enumerate(test_clips, 1):
        print(f"\n📹 CLIP {i}: {clip_data['name']}")
        print(f"   Timeframe: {clip_data['start']}s - {clip_data['end']}s")
        
        # Get segments for this clip
        clip_segments = []
        for segment in segments:
            seg_start = segment.get('start', 0)
            seg_end = segment.get('end', 0)
            
            if not (seg_end <= clip_data['start'] or seg_start >= clip_data['end']):
                clip_segments.append(segment)
        
        print(f"   Found {len(clip_segments)} overlapping segments")
        
        if not clip_segments:
            print(f"   ⚠️  No segments for this timeframe")
            continue
        
        # Show original timing
        first_seg = clip_segments[0]
        original_start = first_seg['start']
        expected_clip_start = original_start - clip_data['start']
        
        print(f"   Original segment: {original_start:.1f}s")
        print(f"   Expected in clip: {expected_clip_start:.1f}s")
        
        # Create actual clip with subtitles
        clip_path = f"uploads/clips/TEST_{clip_data['name']}.mp4"
        
        success = await processor.extract_clip(
            test_video,
            clip_data['start'],
            clip_data['end'],
            clip_path,
            crop_to_vertical=True,
            subtitle_segments=clip_segments
        )
        
        if success and os.path.exists(clip_path):
            file_size = os.path.getsize(clip_path)
            print(f"   ✅ Clip created: {file_size:,} bytes")
            
            # Check if file size suggests subtitles are embedded
            if file_size > 500000:  # > 500KB
                print(f"   ✅ File size suggests subtitles are embedded")
            else:
                print(f"   ⚠️  Small file size - check if subtitles worked")
        else:
            print(f"   ❌ Clip creation failed")
    
    conn.close()
    
    print(f"\n🎉 TEST COMPLETE!")
    print("=" * 70)
    print("✅ 3 test clips created with different timeframes")
    print("✅ Each clip should have subtitles starting from 0:00:00")
    print("✅ Subtitle timing should be adjusted correctly")
    print()
    print("📁 Check the clips in uploads/clips/:")
    print("   - TEST_CLIP_A.mp4")
    print("   - TEST_CLIP_B.mp4") 
    print("   - TEST_CLIP_C.mp4")
    print()
    print("🎬 Download and play these clips to verify subtitles are visible!")

if __name__ == "__main__":
    asyncio.run(final_subtitle_test())
