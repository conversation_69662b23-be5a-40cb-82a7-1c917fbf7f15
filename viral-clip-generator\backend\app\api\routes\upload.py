"""
Upload API routes for video file handling
"""

from fastapi import APIRouter, File, UploadFile, HTTPException, Depends, Form
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
import os
import uuid
import aiofiles
from pathlib import Path
import mimetypes
from typing import Optional
import logging

from ...core.database import get_db
from ...core.config import settings
from ...models.video import Video
from ...services.video_processor import VideoProcessor

logger = logging.getLogger(__name__)
router = APIRouter()

@router.post("/video")
async def upload_video(
    file: UploadFile = File(...),
    title: Optional[str] = Form(None),
    description: Optional[str] = Form(None),
    db: AsyncSession = Depends(get_db)
):
    """Upload a video file for processing"""
    
    # Validate file type
    if not file.content_type or not file.content_type.startswith('video/'):
        raise HTTPException(status_code=400, detail="File must be a video")
    
    # Validate file extension
    file_extension = Path(file.filename).suffix.lower()
    if file_extension not in settings.ALLOWED_VIDEO_EXTENSIONS:
        raise HTTPException(
            status_code=400, 
            detail=f"File extension {file_extension} not allowed. Allowed: {settings.ALLOWED_VIDEO_EXTENSIONS}"
        )
    
    # Generate unique filename
    unique_filename = f"{uuid.uuid4()}{file_extension}"
    file_path = settings.UPLOAD_DIR / "videos" / unique_filename
    
    try:
        # Save file to disk
        async with aiofiles.open(file_path, 'wb') as f:
            content = await file.read()
            
            # Check file size
            if len(content) > settings.MAX_FILE_SIZE:
                raise HTTPException(
                    status_code=413, 
                    detail=f"File too large. Maximum size: {settings.MAX_FILE_SIZE / (1024*1024*1024):.1f}GB"
                )
            
            await f.write(content)
        
        # Get video metadata
        video_processor = VideoProcessor()
        metadata = await video_processor.get_video_metadata(str(file_path))
        
        # Create video record in database
        video = Video(
            filename=unique_filename,
            original_filename=file.filename,
            file_path=str(file_path),
            file_size=len(content),
            duration=metadata.get('duration'),
            width=metadata.get('width'),
            height=metadata.get('height'),
            fps=metadata.get('fps'),
            format=metadata.get('format'),
            title=title,
            description=description,
            status="uploaded"
        )
        
        db.add(video)
        await db.commit()
        await db.refresh(video)
        
        logger.info(f"Video uploaded successfully: {video.id} - {video.original_filename}")
        
        return {
            "message": "Video uploaded successfully",
            "video_id": video.id,
            "filename": video.original_filename,
            "duration": video.duration,
            "size": video.file_size,
            "metadata": metadata
        }
        
    except Exception as e:
        # Clean up file if database operation fails
        if file_path.exists():
            os.remove(file_path)
        
        logger.error(f"Error uploading video: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error uploading video: {str(e)}")

@router.get("/video/{video_id}")
async def get_video_info(video_id: int, db: AsyncSession = Depends(get_db)):
    """Get information about an uploaded video"""
    
    video = await db.get(Video, video_id)
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")
    
    return {
        "id": video.id,
        "filename": video.original_filename,
        "status": video.status,
        "duration": video.duration,
        "size": video.file_size,
        "width": video.width,
        "height": video.height,
        "fps": video.fps,
        "format": video.format,
        "title": video.title,
        "description": video.description,
        "created_at": video.created_at,
        "updated_at": video.updated_at
    }

@router.delete("/video/{video_id}")
async def delete_video(video_id: int, db: AsyncSession = Depends(get_db)):
    """Delete an uploaded video and its associated files"""
    
    video = await db.get(Video, video_id)
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")
    
    try:
        # Delete video file
        if os.path.exists(video.file_path):
            os.remove(video.file_path)
        
        # Delete associated clips and their files
        for clip in video.clips:
            if os.path.exists(clip.file_path):
                os.remove(clip.file_path)
            if clip.thumbnail_path and os.path.exists(clip.thumbnail_path):
                os.remove(clip.thumbnail_path)
        
        # Delete from database
        await db.delete(video)
        await db.commit()
        
        logger.info(f"Video deleted successfully: {video_id}")
        
        return {"message": "Video deleted successfully"}
        
    except Exception as e:
        logger.error(f"Error deleting video {video_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error deleting video: {str(e)}")

@router.get("/videos")
async def list_videos(skip: int = 0, limit: int = 100, db: AsyncSession = Depends(get_db)):
    """List all uploaded videos"""
    
    from sqlalchemy import select
    
    query = select(Video).offset(skip).limit(limit).order_by(Video.created_at.desc())
    result = await db.execute(query)
    videos = result.scalars().all()
    
    return {
        "videos": [
            {
                "id": video.id,
                "filename": video.original_filename,
                "status": video.status,
                "duration": video.duration,
                "size": video.file_size,
                "title": video.title,
                "created_at": video.created_at,
                "clips_count": len(video.clips)
            }
            for video in videos
        ],
        "total": len(videos)
    }
