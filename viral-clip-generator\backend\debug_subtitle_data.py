import asyncio
import os
import sys
import json
from pathlib import Path

# Add the app directory to the Python path
sys.path.append(str(Path(__file__).parent / "app"))

from app.services.video_processor import VideoProcessor

async def debug_subtitle_data():
    """Debug what's happening with subtitle data in the actual clip generation"""
    
    print("🔍 DEBUGGING SUBTITLE DATA FLOW")
    print("=" * 60)
    
    # Check if test video exists
    test_video_path = "uploads/videos/test-video.mp4"
    if not os.path.exists(test_video_path):
        print("❌ Test video not found!")
        return
    
    print(f"✅ Test video found: {test_video_path}")
    
    # Create test segments that match the real data structure
    test_segments = [
        {
            'start': 0.0,
            'end': 3.7,
            'text': 'The problem is today, it can be pushed in a way',
            'words': [
                {'word': 'The', 'start': 0.0, 'end': 0.5},
                {'word': 'problem', 'start': 0.5, 'end': 1.0},
                {'word': 'is', 'start': 1.0, 'end': 1.2},
                {'word': 'today,', 'start': 1.2, 'end': 1.8}
            ]
        },
        {
            'start': 3.7,
            'end': 5.7,
            'text': "that's so different than when we were kids.",
            'words': [
                {'word': "that's", 'start': 3.7, 'end': 4.0},
                {'word': 'so', 'start': 4.0, 'end': 4.2},
                {'word': 'different', 'start': 4.2, 'end': 4.8}
            ]
        }
    ]
    
    processor = VideoProcessor()
    
    print("\n📊 STEP 1: ANALYZING INPUT SEGMENTS")
    print("-" * 50)
    
    print(f"Number of segments: {len(test_segments)}")
    for i, seg in enumerate(test_segments):
        print(f"  Segment {i+1}: {seg['start']:.1f}s - {seg['end']:.1f}s")
        print(f"    Text: '{seg['text']}'")
        print(f"    Words: {len(seg.get('words', []))}")
    
    print("\n🎬 STEP 2: TESTING SUBTITLE FILE CREATION")
    print("-" * 50)
    
    # Test the subtitle file creation directly
    output_dir = Path("uploads/clips")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Test with different clip timeframes
    clip_start = 0.0
    clip_end = 6.0
    
    print(f"Clip timeframe: {clip_start}s - {clip_end}s")
    
    # Call the subtitle creation method directly
    subtitle_file = await processor._create_subtitle_file(test_segments, clip_start, clip_end, output_dir)
    
    if subtitle_file and os.path.exists(subtitle_file):
        print(f"✅ Subtitle file created: {subtitle_file}")
        
        # Read and analyze the content
        with open(subtitle_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"📄 File size: {len(content)} characters")
        print(f"📄 Number of lines: {len(content.splitlines())}")
        
        # Count dialogue lines
        dialogue_lines = [line for line in content.splitlines() if line.startswith('Dialogue:')]
        print(f"📄 Dialogue events: {len(dialogue_lines)}")
        
        print("\n📝 SUBTITLE CONTENT:")
        print("-" * 30)
        print(content)
        print("-" * 30)
        
        # Test if the file is valid by checking for required sections
        has_script_info = '[Script Info]' in content
        has_styles = '[V4+ Styles]' in content
        has_events = '[Events]' in content
        
        print(f"\n✅ ASS file validation:")
        print(f"   Script Info: {'✅' if has_script_info else '❌'}")
        print(f"   Styles:      {'✅' if has_styles else '❌'}")
        print(f"   Events:      {'✅' if has_events else '❌'}")
        print(f"   Dialogues:   {'✅' if dialogue_lines else '❌'}")
        
        if not dialogue_lines:
            print("❌ NO DIALOGUE EVENTS FOUND - THIS IS THE PROBLEM!")
            
            # Debug the segment filtering
            print("\n🔍 DEBUGGING SEGMENT FILTERING:")
            print("-" * 40)
            
            clip_segments = []
            for segment in test_segments:
                seg_start = segment.get('start', 0)
                seg_end = segment.get('end', 0)
                
                print(f"Checking segment: {seg_start}s - {seg_end}s")
                print(f"  Clip range: {clip_start}s - {clip_end}s")
                
                # Check if segment overlaps with clip
                overlaps = not (seg_end <= clip_start or seg_start >= clip_end)
                print(f"  Overlaps: {overlaps}")
                
                if overlaps:
                    # Adjust timing relative to clip start
                    adjusted_start = max(0, seg_start - clip_start)
                    adjusted_end = min(clip_end - clip_start, seg_end - clip_start)
                    
                    print(f"  Adjusted: {adjusted_start}s - {adjusted_end}s")
                    
                    if adjusted_end > adjusted_start:
                        clip_segments.append({
                            'start': adjusted_start,
                            'end': adjusted_end,
                            'text': segment.get('text', '').strip()
                        })
                        print(f"  ✅ Added to clip segments")
                    else:
                        print(f"  ❌ Invalid timing (end <= start)")
                else:
                    print(f"  ❌ No overlap with clip")
            
            print(f"\nFiltered clip segments: {len(clip_segments)}")
            for i, seg in enumerate(clip_segments):
                print(f"  {i+1}: {seg['start']:.1f}s - {seg['end']:.1f}s - '{seg['text']}'")
        
    else:
        print("❌ Failed to create subtitle file!")
    
    print("\n🎬 STEP 3: TESTING FULL CLIP EXTRACTION")
    print("-" * 50)
    
    # Test the full clip extraction
    clip_path = "uploads/clips/DEBUG_SUBTITLE_FLOW.mp4"
    
    success = await processor.extract_clip(
        test_video_path,
        clip_start,
        clip_end,
        clip_path,
        crop_to_vertical=True,
        subtitle_segments=test_segments
    )
    
    if success and os.path.exists(clip_path):
        file_size = os.path.getsize(clip_path)
        print(f"✅ Clip created: {file_size} bytes")
        print(f"📁 Location: {clip_path}")
    else:
        print("❌ Clip creation failed!")
    
    print("\n🎉 SUBTITLE DATA DEBUG COMPLETE!")
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(debug_subtitle_data())
