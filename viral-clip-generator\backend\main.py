"""
Viral Clip Generator - FastAPI Backend
Advanced AI-powered video processing for viral short clips
"""

from fastapi import FastAPI, File, UploadFile, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse
import uvicorn
import os
import asyncio
from pathlib import Path
import json
from typing import List, Dict, Any
import logging

# Import our custom modules
from app.core.config import settings
from app.api.routes import upload, processing, clips
from app.core.database import init_db
from app.services.websocket_manager import WebSocketManager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Viral Clip Generator API",
    description="AI-powered viral video clip generation from long-form content",
    version="1.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc"
)

# CORS middleware for frontend communication
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# WebSocket manager for real-time updates
websocket_manager = WebSocketManager()

# Mount static files for serving processed videos
app.mount("/static", StaticFiles(directory="uploads"), name="static")

# Include API routes
app.include_router(upload.router, prefix="/api/upload", tags=["upload"])
app.include_router(processing.router, prefix="/api/processing", tags=["processing"])
app.include_router(clips.router, prefix="/api/clips", tags=["clips"])

@app.on_event("startup")
async def startup_event():
    """Initialize database and create necessary directories"""
    logger.info("Starting Viral Clip Generator API...")
    
    # Initialize database
    await init_db()
    
    # Create necessary directories
    os.makedirs("uploads", exist_ok=True)
    os.makedirs("uploads/videos", exist_ok=True)
    os.makedirs("uploads/clips", exist_ok=True)
    os.makedirs("uploads/temp", exist_ok=True)
    
    logger.info("API startup complete!")

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    logger.info("Shutting down Viral Clip Generator API...")

@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "message": "Viral Clip Generator API",
        "status": "running",
        "version": "1.0.0"
    }

@app.get("/api/health")
async def health_check():
    """Detailed health check"""
    return {
        "status": "healthy",
        "services": {
            "database": "connected",
            "storage": "available",
            "ai_models": "loaded"
        }
    }

@app.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    """WebSocket endpoint for real-time processing updates"""
    await websocket_manager.connect(websocket, client_id)
    try:
        while True:
            # Keep connection alive and handle incoming messages
            data = await websocket.receive_text()
            message = json.loads(data)
            
            # Echo back for now - can be extended for client commands
            await websocket_manager.send_personal_message(
                json.dumps({"type": "echo", "data": message}), 
                client_id
            )
    except WebSocketDisconnect:
        websocket_manager.disconnect(client_id)

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
