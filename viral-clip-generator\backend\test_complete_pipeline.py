#!/usr/bin/env python3
"""
Test the complete viral clip generation pipeline with fast models
"""

import asyncio
import sys
import logging
import time
from app.services.ai_processor import AIProcessor
from app.services.websocket_manager import WebSocketManager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_complete_pipeline():
    """Test the complete AI processing pipeline with fast models"""
    
    print("🚀 TESTING COMPLETE VIRAL CLIP PIPELINE")
    print("=" * 60)
    
    try:
        # Initialize components
        print("🔧 Initializing components...")
        websocket_manager = WebSocketManager()
        ai_processor = AIProcessor(websocket_manager, client_id="test-client")
        
        print("⚡ Loading AI models (fast configuration)...")
        start_time = time.time()
        await ai_processor.load_models()
        load_time = time.time() - start_time
        print(f"✅ All AI models loaded in {load_time:.1f} seconds!")
        
        # Test 1: Advanced Text Analysis
        print("\n📝 TESTING ADVANCED TEXT ANALYSIS")
        print("-" * 40)
        
        test_texts = [
            "This is absolutely mind-blowing! You won't believe what happens next!",
            "The secret technique that will change everything forever!",
            "Wait for it... the most amazing transformation you've ever seen!",
            "This simple trick will shock you! Doctors hate this one weird trick!"
        ]
        
        for i, text in enumerate(test_texts, 1):
            print(f"\n🔍 Analyzing text {i}: '{text[:50]}...'")
            
            # Sentiment analysis
            sentiment = await ai_processor._analyze_sentiment_advanced(text)
            print(f"   🎭 Sentiment: {sentiment}")
            
            # Emotion analysis  
            emotion = await ai_processor._analyze_emotion_advanced(text)
            print(f"   😊 Emotion: {emotion}")
            
            # LM Studio analysis
            if ai_processor.lm_studio_client:
                lm_analysis = await ai_processor._analyze_with_lm_studio(text)
                print(f"   🤖 LM Studio: {lm_analysis}")
            
            # Text features
            features = await ai_processor._analyze_text_features(text)
            print(f"   📊 Features Score: {features:.2f}")
            
            # Keywords
            keywords = await ai_processor._extract_keywords(text)
            print(f"   🔑 Keywords: {keywords}")
        
        # Test 2: Viral Moment Detection
        print("\n\n🎯 TESTING VIRAL MOMENT DETECTION")
        print("-" * 40)
        
        # Create mock transcript with viral content
        mock_transcript = {
            "segments": [
                {
                    "start": 0.0,
                    "end": 15.0,
                    "text": "This is absolutely incredible! You won't believe what happens next!"
                },
                {
                    "start": 15.0,
                    "end": 30.0,
                    "text": "The secret technique that changed everything. This will blow your mind!"
                },
                {
                    "start": 30.0,
                    "end": 45.0,
                    "text": "Wait for it... the most amazing transformation you've ever seen!"
                },
                {
                    "start": 45.0,
                    "end": 60.0,
                    "text": "This simple trick will shock you! Doctors hate this one weird trick!"
                },
                {
                    "start": 60.0,
                    "end": 75.0,
                    "text": "And that's how you can change your life in just 30 seconds!"
                }
            ]
        }
        
        print(f"📹 Processing {len(mock_transcript['segments'])} transcript segments...")
        viral_moments = await ai_processor._detect_viral_moments(mock_transcript)
        
        print(f"\n🔥 DETECTED {len(viral_moments)} VIRAL MOMENTS:")
        print("=" * 50)
        
        for i, moment in enumerate(viral_moments):
            print(f"\n📹 VIRAL MOMENT #{i+1}")
            print(f"   ⏱️  Time: {moment['start_time']:.1f}s - {moment['end_time']:.1f}s")
            print(f"   📝 Text: {moment['text']}")
            print(f"   🎯 Viral Score: {moment['viral_score']:.3f}")
            print(f"   🎭 Sentiment: {moment['sentiment']} ({moment['sentiment_score']:.2f})")
            print(f"   😊 Emotion: {moment['emotion']} ({moment['emotion_score']:.2f})")
            
            if 'lm_analysis' in moment and moment['lm_analysis']:
                lm_data = moment['lm_analysis']
                print(f"   🤖 LM Analysis:")
                print(f"      - Viral Potential: {lm_data.get('viral_potential', 'N/A')}")
                print(f"      - Reasoning: {lm_data.get('reasoning', 'N/A')[:100]}...")
            
            if 'keywords' in moment:
                print(f"   🔑 Keywords: {moment['keywords']}")
        
        # Test 3: Performance Metrics
        print(f"\n\n📊 PERFORMANCE METRICS")
        print("-" * 40)
        
        total_segments = len(mock_transcript['segments'])
        viral_segments = len(viral_moments)
        viral_percentage = (viral_segments / total_segments) * 100
        
        print(f"📈 Total Segments: {total_segments}")
        print(f"🔥 Viral Segments: {viral_segments}")
        print(f"📊 Viral Detection Rate: {viral_percentage:.1f}%")
        
        # Calculate average scores
        if viral_moments:
            avg_viral_score = sum(m['viral_score'] for m in viral_moments) / len(viral_moments)
            avg_sentiment = sum(m['sentiment_score'] for m in viral_moments) / len(viral_moments)
            avg_emotion = sum(m['emotion_score'] for m in viral_moments) / len(viral_moments)
            
            print(f"🎯 Average Viral Score: {avg_viral_score:.3f}")
            print(f"🎭 Average Sentiment Score: {avg_sentiment:.3f}")
            print(f"😊 Average Emotion Score: {avg_emotion:.3f}")
        
        print("\n" + "=" * 60)
        print("🎉 COMPLETE PIPELINE TEST SUCCESSFUL!")
        print("🚀 VIRAL CLIP GENERATOR IS READY!")
        print("🔥 All advanced AI models working perfectly!")
        print("⚡ Fast loading and processing optimized!")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"❌ Pipeline test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    result = asyncio.run(test_complete_pipeline())
    sys.exit(0 if result else 1)
