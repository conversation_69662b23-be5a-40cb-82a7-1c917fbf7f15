#!/usr/bin/env python3
"""
Test script to verify our fixed AI processor works with real video processing
"""

import asyncio
import sys
import os
import json
from pathlib import Path

# Add the app directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.services.ai_processor import AIProcessor
from app.services.websocket_manager import WebSocketManager
from app.core.database import AsyncSessionLocal, init_db
from app.models.video import Video
from app.models.clip import Clip
from sqlalchemy import select

async def test_fixed_processor():
    """Test the fixed AI processor with the test video"""
    print("🧪 Testing Fixed AI Processor with Real Video")
    print("=" * 50)
    
    # Initialize database
    await init_db()

    # Create websocket manager and AI processor
    websocket_manager = WebSocketManager()
    processor = AIProcessor(websocket_manager)
    
    # Test video path
    test_video_path = "uploads/videos/test-video.mp4"
    
    if not os.path.exists(test_video_path):
        print(f"❌ Test video not found: {test_video_path}")
        return
    
    print(f"✅ Test video found: {test_video_path}")
    print(f"📁 File size: {os.path.getsize(test_video_path)} bytes")
    
    # Create a test video record
    async with AsyncSessionLocal() as db:
        # Create video record
        video = Video(
            filename="test-video.mp4",
            original_filename="TEST FILE.mp4",
            file_path=test_video_path,
            file_size=os.path.getsize(test_video_path),
            duration=60.0,  # Assume 60 seconds
            width=1920,
            height=1080,
            fps=30.0,
            format="mp4",
            status="uploaded"
        )
        
        db.add(video)
        await db.commit()
        await db.refresh(video)
        
        print(f"✅ Created video record: ID {video.id}")
        
        try:
            # Create a processing job
            from app.models.processing_job import ProcessingJob
            job = ProcessingJob(
                video_id=video.id,
                job_type="full_pipeline",
                status="pending",
                progress=0.0
            )
            db.add(job)
            await db.commit()
            await db.refresh(job)

            # Test the complete pipeline
            print("\n🎤 Testing Audio Transcription...")
            result = await processor.process_video(video.id, job.id)
            
            print(f"\n📊 Processing Result:")
            print(f"   Status: {result.get('status', 'Unknown')}")
            print(f"   Clips Generated: {result.get('clips_generated', 0)}")
            
            if result.get('error'):
                print(f"   ❌ Error: {result['error']}")
            
            # Check what clips were created
            clips_query = select(Clip).where(Clip.video_id == video.id)
            clips_result = await db.execute(clips_query)
            clips = clips_result.scalars().all()
            
            print(f"\n🎬 Generated Clips: {len(clips)}")
            for i, clip in enumerate(clips):
                print(f"   Clip {i+1}:")
                print(f"     Title: {clip.title}")
                print(f"     Duration: {clip.duration}s")
                print(f"     Viral Score: {clip.viral_score}")
                print(f"     Sentiment: {clip.sentiment}")
                print(f"     Emotion: {clip.emotion}")
                print(f"     File: {clip.file_path}")
                print()
            
            # Update video status
            video.status = "completed"
            await db.commit()
            
            print("✅ Test completed successfully!")
            
        except Exception as e:
            print(f"❌ Test failed with error: {str(e)}")
            import traceback
            print(f"📋 Full traceback:\n{traceback.format_exc()}")
            
            # Update video status to failed
            video.status = "failed"
            await db.commit()

if __name__ == "__main__":
    asyncio.run(test_fixed_processor())
