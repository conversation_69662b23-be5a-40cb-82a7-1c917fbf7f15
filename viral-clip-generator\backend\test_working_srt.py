#!/usr/bin/env python3

import asyncio
import os
import sys
import subprocess
from pathlib import Path

# Add the app directory to Python path
sys.path.append(str(Path(__file__).parent / 'app'))

from app.services.video_processor import VideoProcessor

async def test_working_srt():
    """Test with the working SRT file we created manually"""
    
    print("🎬 TESTING WITH WORKING SRT FILE")
    print("=" * 70)
    
    # Use the working SRT file we created
    working_srt = "uploads/clips/DEBUG_SRT_MANUAL.srt"
    
    if not os.path.exists(working_srt):
        print("❌ Working SRT file not found - run debug_srt_generation_detailed.py first")
        return
    
    # Get a video file
    videos_dir = Path("uploads/videos")
    video_files = list(videos_dir.glob("*.mp4"))
    
    if not video_files:
        print("❌ No video files found")
        return
    
    video_path = str(video_files[0])
    print(f"✅ Video: {video_path}")
    print(f"✅ SRT: {working_srt}")
    
    # Check SRT content
    with open(working_srt, 'r', encoding='utf-8') as f:
        srt_content = f.read()
    
    print(f"📝 SRT content ({len(srt_content)} chars):")
    print(srt_content[:200] + "..." if len(srt_content) > 200 else srt_content)
    
    # Build FFmpeg command manually
    start_time = 10.0
    end_time = 20.0
    output_path = "uploads/clips/WORKING_SRT_TEST.mp4"
    
    cmd = [
        'ffmpeg',
        '-i', video_path,
        '-i', working_srt,
        '-ss', str(start_time),
        '-t', str(end_time - start_time),
        '-c:v', 'libx264',
        '-c:a', 'aac',
        '-preset', 'fast',
        '-crf', '23',
        '-vf', 'crop=ih*9/16:ih',
        '-map', '0:v', '-map', '0:a', '-map', '1:s',
        '-c:s', 'mov_text',
        '-y', output_path
    ]
    
    print(f"\n🎯 FFMPEG COMMAND:")
    print(" ".join(cmd))
    
    print(f"\n⚡ EXECUTING FFMPEG:")
    print("-" * 50)
    
    try:
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=60
        )
        
        print(f"📊 Return code: {result.returncode}")
        
        if result.returncode == 0 and os.path.exists(output_path):
            file_size = os.path.getsize(output_path)
            print(f"✅ SUCCESS! File created: {file_size:,} bytes")
            
            # Check for subtitle streams
            print(f"\n🔍 CHECKING SUBTITLE STREAMS:")
            
            probe_cmd = [
                "ffprobe", 
                "-v", "quiet", 
                "-print_format", "json", 
                "-show_streams", 
                output_path
            ]
            
            probe_result = subprocess.run(probe_cmd, capture_output=True, text=True, timeout=30)
            
            if probe_result.returncode == 0:
                import json
                data = json.loads(probe_result.stdout)
                streams = data.get('streams', [])
                
                subtitle_streams = [s for s in streams if s.get('codec_type') == 'subtitle']
                
                if subtitle_streams:
                    print(f"🎉 SUBTITLE STREAMS FOUND: {len(subtitle_streams)}")
                    for i, sub_stream in enumerate(subtitle_streams):
                        codec = sub_stream.get('codec_name', 'unknown')
                        print(f"   Stream {i}: {codec}")
                    
                    print(f"\n🎉 SUCCESS! SUBTITLES WILL APPEAR/DISAPPEAR!")
                    print(f"✅ Download and test: {output_path}")
                else:
                    print(f"❌ No subtitle streams found")
            
        else:
            print(f"❌ FAILED!")
            if result.stderr:
                print(f"Error: {result.stderr[:500]}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print(f"\n🎯 SUMMARY")
    print("=" * 70)
    print("✅ This test uses a known working SRT file")
    print("✅ If successful, the issue is in SRT generation, not FFmpeg")
    print("🎬 The fix is to ensure SRT generation works with real data")

if __name__ == "__main__":
    asyncio.run(test_working_srt())
