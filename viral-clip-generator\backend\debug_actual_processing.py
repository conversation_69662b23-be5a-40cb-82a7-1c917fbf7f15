import asyncio
import os
import sys
import json
import sqlite3
from pathlib import Path

# Add the app directory to the Python path
sys.path.append(str(Path(__file__).parent / "app"))

async def debug_actual_processing():
    """Debug what's actually happening in the real processing pipeline"""
    
    print("🔍 DEBUGGING ACTUAL PROCESSING PIPELINE")
    print("=" * 60)
    
    # Check the most recent video and its transcript data
    conn = sqlite3.connect('viral_clips.db')
    cursor = conn.cursor()
    
    # Get the most recent video
    cursor.execute('SELECT * FROM videos ORDER BY id DESC LIMIT 1')
    video_data = cursor.fetchone()
    
    if not video_data:
        print("❌ No videos found!")
        return
    
    video_id = video_data[0]
    filename = video_data[1]
    file_path = video_data[3]
    transcript_with_timestamps = video_data[12]  # Adjust index based on schema
    
    print(f"✅ Most recent video: ID {video_id} - {filename}")
    print(f"📁 Path: {file_path}")
    
    # Check if transcript data exists
    if not transcript_with_timestamps:
        print("❌ No transcript data found!")
        return
    
    try:
        # Parse transcript data
        transcript_data = json.loads(transcript_with_timestamps)
        segments = transcript_data.get('segments', [])
        
        print(f"📝 Transcript segments: {len(segments)}")
        
        if not segments:
            print("❌ No segments in transcript!")
            return
        
        # Check if segments have words
        segments_with_words = [s for s in segments if 'words' in s and s['words']]
        print(f"📝 Segments with words: {len(segments_with_words)}/{len(segments)}")
        
        if len(segments_with_words) == 0:
            print("❌ NO SEGMENTS HAVE WORDS - THIS IS THE PROBLEM!")
            print("🔧 The transcript data in the database is from BEFORE the fix!")
            print()
            print("📋 Sample segment structure:")
            if segments:
                sample_seg = segments[0]
                print(f"   Keys: {list(sample_seg.keys())}")
                print(f"   Text: '{sample_seg.get('text', '')[:50]}...'")
                print(f"   Has words: {'words' in sample_seg}")
            
            print()
            print("🚀 SOLUTION: You need to RE-PROCESS the video to get new transcript data with words!")
            print("   1. Delete the existing transcript data")
            print("   2. Re-upload and process the video")
            print("   3. The new processing will use the fixed transcription code")
            
        else:
            print("✅ Segments have words - checking sample:")
            sample_seg = segments_with_words[0]
            words = sample_seg['words']
            print(f"   Sample segment: '{sample_seg['text'][:50]}...'")
            print(f"   Word count: {len(words)}")
            if words:
                print(f"   First word: '{words[0]['word']}' ({words[0]['start']:.1f}s)")
                print(f"   Last word: '{words[-1]['word']}' ({words[-1]['start']:.1f}s)")
        
        # Check recent clips
        cursor.execute('SELECT * FROM clips WHERE video_id = ? ORDER BY id DESC LIMIT 3', (video_id,))
        clips = cursor.fetchall()
        
        print(f"\n🎬 Recent clips for this video: {len(clips)}")
        for clip in clips:
            clip_id = clip[0]
            clip_filename = clip[2]
            has_subtitles = clip[17]  # Adjust index
            file_size = clip[4]
            created_at = clip[25]  # Adjust index
            
            print(f"   Clip {clip_id}: {clip_filename}")
            print(f"      Has subtitles: {has_subtitles}")
            print(f"      File size: {file_size:,} bytes")
            print(f"      Created: {created_at}")
            
            # Check if file exists
            clip_path = clip[3]
            if os.path.exists(clip_path):
                actual_size = os.path.getsize(clip_path)
                print(f"      ✅ File exists: {actual_size:,} bytes")
            else:
                print(f"      ❌ File not found")
            print()
        
    except json.JSONDecodeError as e:
        print(f"❌ Failed to parse transcript data: {e}")
        print(f"📄 Raw data type: {type(transcript_with_timestamps)}")
        print(f"📄 Raw data preview: {str(transcript_with_timestamps)[:200]}...")
    
    conn.close()
    
    print("\n🎯 DIAGNOSIS:")
    print("=" * 60)
    print("If segments don't have words, the issue is:")
    print("1. ❌ The video was processed BEFORE the subtitle fix")
    print("2. ❌ The transcript data in database lacks word-level timing")
    print("3. ❌ Subtitle generation falls back to estimated timing")
    print()
    print("🔧 SOLUTION:")
    print("1. Clear the transcript data for this video")
    print("2. Re-process the video with the fixed code")
    print("3. New clips will have proper word-level subtitles")

if __name__ == "__main__":
    asyncio.run(debug_actual_processing())
