#!/usr/bin/env python3

import asyncio
import os
import sys
import json
from pathlib import Path

# Add the app directory to Python path
sys.path.append(str(Path(__file__).parent / 'app'))

from app.services.video_processor import VideoProcessor

async def test_real_subtitle_timing():
    """Test subtitle timing with real transcript data"""
    
    print("🔍 TESTING REAL SUBTITLE TIMING FIX")
    print("=" * 60)
    
    # Get real transcript data from database
    import sqlite3
    conn = sqlite3.connect('viral_clips.db')
    cursor = conn.cursor()
    
    # Get the most recent video with transcript data
    cursor.execute('''
        SELECT id, transcript_with_timestamps 
        FROM videos 
        WHERE transcript_with_timestamps IS NOT NULL 
        ORDER BY id DESC 
        LIMIT 1
    ''')
    
    result = cursor.fetchone()
    if not result:
        print("❌ No videos with transcript data found")
        return
    
    video_id, transcript_json = result
    transcript_data = json.loads(transcript_json)
    segments = transcript_data.get('segments', [])
    
    print(f"✅ Found video {video_id} with {len(segments)} transcript segments")
    
    if len(segments) < 10:
        print("❌ Not enough segments for testing")
        return
    
    # Test with 3 different time ranges using real data
    test_clips = [
        {
            "name": "EARLY_CLIP",
            "start": 10.0,
            "end": 20.0,
            "description": "Early clip (10-20s)"
        },
        {
            "name": "MIDDLE_CLIP", 
            "start": 60.0,
            "end": 75.0,
            "description": "Middle clip (60-75s)"
        },
        {
            "name": "LATE_CLIP",
            "start": 120.0,
            "end": 135.0,
            "description": "Late clip (120-135s)"
        }
    ]
    
    processor = VideoProcessor()
    
    print(f"\n🎬 TESTING SUBTITLE TIMING WITH REAL DATA")
    print("-" * 60)
    
    for i, clip_data in enumerate(test_clips, 1):
        print(f"\n📹 TEST {i}: {clip_data['name']}")
        print(f"   {clip_data['description']}")
        print(f"   Time range: {clip_data['start']}s - {clip_data['end']}s")
        
        # Filter segments for this clip
        clip_segments = []
        for segment in segments:
            seg_start = segment.get('start', 0)
            seg_end = segment.get('end', 0)
            
            # Check if segment overlaps with clip timeframe
            if not (seg_end <= clip_data['start'] or seg_start >= clip_data['end']):
                clip_segments.append(segment)
        
        print(f"   Found {len(clip_segments)} overlapping segments")
        
        if not clip_segments:
            print(f"   ⚠️  No segments found for this time range")
            continue
        
        # Show original timing of first segment
        first_seg = clip_segments[0]
        print(f"   Original segment: {first_seg['start']:.1f}s-{first_seg['end']:.1f}s")
        print(f"   Text: '{first_seg['text'][:50]}...'")
        
        # Create subtitle file
        output_dir = Path("uploads/clips")
        subtitle_file = await processor._create_subtitle_file(
            clip_segments, 
            clip_data['start'], 
            clip_data['end'], 
            output_dir
        )
        
        if subtitle_file and os.path.exists(subtitle_file):
            print(f"   ✅ Subtitle file created: {os.path.basename(subtitle_file)}")
            
            # Read and check the timing
            with open(subtitle_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Extract dialogue lines
            dialogue_lines = [line for line in content.splitlines() if line.startswith('Dialogue:')]
            
            if dialogue_lines:
                first_dialogue = dialogue_lines[0]
                # Extract timing from dialogue line
                # Format: Dialogue: 0,Start,End,Style,Name,MarginL,MarginR,MarginV,Effect,Text
                parts = first_dialogue.split(',')
                if len(parts) >= 3:
                    start_time = parts[1]
                    end_time = parts[2]
                    print(f"   📝 ASS timing: {start_time} - {end_time}")
                    
                    # Check if timing starts near 0 (correct for clip)
                    if start_time.startswith('0:00:00') or start_time.startswith('0:00:01'):
                        print(f"   ✅ Timing looks correct (starts near 0)")
                    else:
                        print(f"   ❌ Timing looks wrong (should start near 0)")
                else:
                    print(f"   ❌ Could not parse dialogue timing")
            else:
                print(f"   ❌ No dialogue lines found")
        else:
            print(f"   ❌ Subtitle file creation failed")
    
    conn.close()
    
    print(f"\n🎯 SUMMARY")
    print("=" * 60)
    print("✅ If timing shows 0:00:00-0:00:XX, the fix is working")
    print("❌ If timing shows original video timestamps, the fix failed")
    print("🔍 Check the ASS timing output above")

if __name__ == "__main__":
    asyncio.run(test_real_subtitle_timing())
